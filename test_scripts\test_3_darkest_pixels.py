#!/usr/bin/env python3
"""
Test du calcul automatique du threshold avec les 3 pixels les plus foncés
"""

import numpy as np
from utils.helpers import calculate_auto_threshold

def test_3_darkest_pixels():
    print("=== Test du calcul automatique du threshold (3 pixels les plus foncés) ===")
    
    # Test 1: Un seul pixel noir
    print("\n1. Test avec un seul pixel noir:")
    test_image_1 = np.full((10, 10), 255, dtype=np.uint8)  # Tout blanc
    test_image_1[5, 5] = 1  # Un pixel noir
    
    polygon_points = [(0, 0), (10, 0), (10, 10), (0, 10), (0, 0)]
    threshold_1 = calculate_auto_threshold(test_image_1, polygon_points, 0)
    print(f"Threshold calculé: {threshold_1} (devrait être 1)")
    
    # Test 2: Trois pixels noirs identiques
    print("\n2. Test avec trois pixels noirs identiques:")
    test_image_2 = np.full((10, 10), 255, dtype=np.uint8)  # Tout blanc
    test_image_2[5, 4] = 1  # Premier pixel noir
    test_image_2[5, 5] = 1  # Deuxième pixel noir
    test_image_2[5, 6] = 1  # Troisième pixel noir
    
    threshold_2 = calculate_auto_threshold(test_image_2, polygon_points, 0)
    print(f"Threshold calculé: {threshold_2} (devrait être 1)")
    
    # Test 3: Pixels de valeurs différentes
    print("\n3. Test avec pixels de valeurs différentes:")
    test_image_3 = np.full((10, 10), 255, dtype=np.uint8)  # Tout blanc
    test_image_3[5, 4] = 1   # Pixel le plus foncé
    test_image_3[5, 5] = 2   # Deuxième plus foncé
    test_image_3[5, 6] = 5   # Troisième plus foncé
    test_image_3[5, 7] = 50  # Plus clair (ne devrait pas être pris)
    
    threshold_3 = calculate_auto_threshold(test_image_3, polygon_points, 0)
    print(f"Threshold calculé: {threshold_3} (devrait être 5 = max des 3 plus foncés)")
    
    # Test 4: Avec pourcentage
    print("\n4. Test avec pourcentage 20%:")
    threshold_4 = calculate_auto_threshold(test_image_3, polygon_points, 20)
    expected_4 = 5 + (20/100.0) * 5  # 5 + 1 = 6
    print(f"Threshold calculé: {threshold_4} (attendu: {expected_4})")
    
    # Test 5: Zone avec beaucoup de pixels
    print("\n5. Test avec zone plus large:")
    test_image_5 = np.full((20, 20), 255, dtype=np.uint8)  # Tout blanc
    # Créer une zone avec différentes valeurs
    test_image_5[10:15, 10:15] = 100  # Zone grise
    test_image_5[11, 11] = 10  # Pixel foncé
    test_image_5[12, 12] = 15  # Deuxième pixel foncé
    test_image_5[13, 13] = 20  # Troisième pixel foncé
    
    polygon_points_5 = [(9, 9), (16, 9), (16, 16), (9, 16), (9, 9)]
    threshold_5 = calculate_auto_threshold(test_image_5, polygon_points_5, 0)
    print(f"Threshold calculé: {threshold_5} (devrait être 20 = max des 3 plus foncés)")
    
    # Test de vérification des pixels capturés
    print("\n6. Vérification des pixels capturés:")
    binary_mask = (test_image_3 <= threshold_3).astype(np.uint8) * 255
    pixels_captured = np.sum(binary_mask == 255)
    y_coords, x_coords = np.where(binary_mask == 255)
    print(f"Pixels capturés avec threshold {threshold_3}: {pixels_captured}")
    print(f"Coordonnées: {list(zip(x_coords, y_coords))}")
    
    # Test 7: Cas extrême - un seul pixel avec pourcentage 0%
    print("\n7. Test cas extrême - un seul pixel avec 0%:")
    test_image_7 = np.full((5, 5), 255, dtype=np.uint8)  # Tout blanc
    test_image_7[2, 2] = 10  # Un seul pixel foncé
    
    polygon_points_7 = [(0, 0), (5, 0), (5, 5), (0, 5), (0, 0)]
    threshold_7 = calculate_auto_threshold(test_image_7, polygon_points_7, 0)
    print(f"Threshold calculé: {threshold_7} (devrait être 10)")
    
    # Vérifier que ce pixel est capturé
    binary_mask_7 = (test_image_7 <= threshold_7).astype(np.uint8) * 255
    pixels_captured_7 = np.sum(binary_mask_7 == 255)
    print(f"Pixels capturés: {pixels_captured_7} (devrait être au moins 1)")
    
    print("\n=== Vérifications ===")
    
    if threshold_1 == 1:
        print("✅ SUCCÈS: Un pixel noir donne threshold = 1")
    else:
        print(f"❌ ÉCHEC: Un pixel noir donne threshold = {threshold_1} au lieu de 1")
    
    if threshold_2 == 1:
        print("✅ SUCCÈS: Trois pixels noirs identiques donnent threshold = 1")
    else:
        print(f"❌ ÉCHEC: Trois pixels noirs identiques donnent threshold = {threshold_2} au lieu de 1")
        
    if threshold_3 == 5:
        print("✅ SUCCÈS: Pixels [1,2,5] donnent threshold = 5")
    else:
        print(f"❌ ÉCHEC: Pixels [1,2,5] donnent threshold = {threshold_3} au lieu de 5")
        
    if pixels_captured >= 3:
        print("✅ SUCCÈS: Au moins 3 pixels sont capturés")
    else:
        print(f"❌ ÉCHEC: Seulement {pixels_captured} pixels capturés au lieu d'au moins 3")
        
    if pixels_captured_7 >= 1:
        print("✅ SUCCÈS: Le pixel isolé est bien capturé")
    else:
        print("❌ ÉCHEC: Le pixel isolé n'est pas capturé")
    
    print("\n=== Test terminé ===")

if __name__ == "__main__":
    test_3_darkest_pixels()
