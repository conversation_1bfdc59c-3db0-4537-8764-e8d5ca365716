# 🏷️ Guide : ROI Liées aux Labels Persistants

## 🎯 **Nouvelle Fonctionnalité Implémentée**

### **Liaison ROI-Label**
Les **zones ROI sont maintenant liées au label** avec lequel vous les dessinez :
- **frontwall** (bleu)
- **backwall** (vert) 
- **flaw** (rouge)
- **indication** (orange)

### **Persistance Complète**
- **Label sauvegardé** avec chaque ROI
- **Label restauré** lors du recalcul
- **Polygones ajoutés au bon label** automatiquement

## 🔄 **Fonctionnement**

### **Création d'une ROI**
```
1. Sélectionner le label souhaité (Espace pour changer)
   → Interface affiche : "Label actuel: frontwall" (bleu)

2. Dessiner la zone ROI (rectangle ou polygone)
   → ROI sauvegardée avec le label 'frontwall'
   → Message : "💾 ROI SAUVEGARDÉE: Zone polygon pour label 'frontwall'"

3. Valider avec Enter
   → Polygones ajoutés au label 'frontwall'
   → ROI reste active pour les prochaines images
```

### **Changement d'Image avec ROI**
```
1. Charger nouvelle image
   → ROI restaurée avec son label original

2. Cliquer [Recalculer ROI]
   → Interface change temporairement : "Label actuel: frontwall (ROI)"
   → Calcul effectué avec le label 'frontwall'
   → Polygones temporaires créés pour 'frontwall'

3. Valider avec Enter
   → Polygones ajoutés au label 'frontwall'
   → Interface revient au label original
```

## 🏷️ **Gestion des Labels**

### **Sélection du Label**
```
Méthodes pour changer de label :
• Touche Espace : Cycle entre frontwall → backwall → flaw → indication
• Interface : Affichage en temps réel du label actuel avec couleur
```

### **Labels Disponibles**
```
frontwall  : Bleu    (#0000FF) - Paroi avant
backwall   : Vert    (#00FF00) - Paroi arrière  
flaw       : Rouge   (#FF0000) - Défaut
indication : Orange  (#FFA500) - Indication
```

### **Affichage des Labels**
```
Interface ROI : "2 ROI persistante(s) [frontwall, flaw] (ROI 1: frontwall active)"
                                      ↑                    ↑
                                   Labels des ROI      Label de la ROI active
```

## 🎯 **Workflow avec Labels**

### **Cas 1 : ROI pour Différents Labels**
```
Image 1 :
1. Label 'frontwall' → Dessiner ROI zone A → Valider
2. Changer label 'flaw' → Dessiner ROI zone B → Valider
   → 2 ROI sauvegardées : zone A (frontwall), zone B (flaw)

Images suivantes :
1. Charger image → 2 ROI restaurées
2. [Recalculer ROI] → 
   • Zone A recalculée avec label 'frontwall'
   • Zone B recalculée avec label 'flaw'
3. Valider → Polygones ajoutés aux bons labels
```

### **Cas 2 : Inspection Spécialisée**
```
Inspection frontwall :
1. Label 'frontwall' → Créer ROI → Traiter série d'images
   → Tous les polygones ajoutés au label 'frontwall'

Inspection défauts :
1. Label 'flaw' → Créer ROI → Traiter série d'images
   → Tous les polygones ajoutés au label 'flaw'
```

### **Cas 3 : ROI Multiples Même Label**
```
1. Label 'flaw' → Dessiner ROI zone 1 → Valider
2. Label 'flaw' → Dessiner ROI zone 2 → Valider
   → 2 ROI pour le même label 'flaw'

Changement d'image :
→ Les 2 zones recalculées avec label 'flaw'
→ Tous les polygones ajoutés au label 'flaw'
```

## 🔧 **Implémentation Technique**

### **Sauvegarde du Label**
```python
roi_data = {
    'points': points.copy(),
    'type': roi_type,
    'label': current_label,  # Label sauvegardé avec la ROI
    'parameters': { ... }
}
```

### **Restauration avec Label**
```python
def _reproduce_rectangle_creation_event(self, points, parameters, roi_label):
    # Sauvegarder label actuel
    original_label = self._controller.get_current_label()
    
    # Changer temporairement pour le label de la ROI
    self._controller._model._current_label = roi_label
    
    # Effectuer le calcul (polygones ajoutés au bon label)
    # ...
    
    # Restaurer le label original
    self._controller._model._current_label = original_label
```

### **Affichage Temporaire**
```python
# Pendant le recalcul ROI
self._current_label_display.config(
    text=f"Label actuel: {roi_label} (ROI)",
    fg=self.LABEL_COLORS[roi_label]
)

# Après le recalcul
self._current_label_display.config(
    text=f"Label actuel: {original_label}",
    fg=self.LABEL_COLORS[original_label]
)
```

## 🎯 **Avantages du Système**

### **🏷️ Organisation Parfaite**
- **Séparation claire** : Chaque ROI liée à son label spécifique
- **Pas de confusion** : Polygones ajoutés au bon label automatiquement
- **Traçabilité** : Historique complet label → ROI → polygones
- **Flexibilité** : ROI différentes pour labels différents

### **⚡ Workflow Optimisé**
- **Inspection spécialisée** : ROI dédiées par type de défaut
- **Traitement en série** : Même ROI appliquée avec bon label
- **Validation cohérente** : Résultats organisés par label
- **Export structuré** : Masques séparés par label

### **🎯 Précision Garantie**
- **Label correct** : Impossible d'ajouter au mauvais label
- **Cohérence** : ROI toujours associée à son label d'origine
- **Reproductibilité** : Même comportement sur toutes les images
- **Fiabilité** : Pas d'erreur de classification

## 📊 **Exemples Concrets**

### **Exemple 1 : Inspection Frontwall**
```
Objectif : Détecter défauts sur paroi avant

Workflow :
1. Label 'frontwall' → Dessiner ROI zone critique
2. Traiter 100 images → ROI recalculée automatiquement
3. Tous les polygones → Label 'frontwall' (bleu)
4. Export → Masque frontwall.png avec défauts détectés
```

### **Exemple 2 : Inspection Multi-Labels**
```
Objectif : Détecter différents types de défauts

Workflow :
1. Label 'flaw' → ROI zone défauts critiques
2. Label 'indication' → ROI zone indications
3. Traiter série → Chaque ROI avec son label
4. Export → 2 masques séparés (flaw.png, indication.png)
```

### **Exemple 3 : Contrôle Qualité Complet**
```
Objectif : Inspection complète multi-zones

Workflow :
1. Label 'frontwall' → ROI paroi avant
2. Label 'backwall' → ROI paroi arrière  
3. Label 'flaw' → ROI zone critique
4. Traiter série → 3 ROI avec labels respectifs
5. Export → 3 masques organisés par type
```

## 🎨 **Interface Utilisateur**

### **Affichage Label Actuel**
```
Normal : "Label actuel: frontwall" (bleu)
ROI    : "Label actuel: frontwall (ROI)" (bleu)
```

### **Information ROI**
```
Aucune ROI : "Aucune ROI active"
1 ROI      : "1 ROI persistante(s) [frontwall] (ROI 1: frontwall active)"
2 ROI      : "2 ROI persistante(s) [frontwall, flaw] (ROI 1: frontwall active)"
```

### **Messages Console**
```
Création : "💾 ROI SAUVEGARDÉE: Zone polygon pour label 'frontwall'"
Recalcul : "🔄 Reproduction de l'événement pour ROI 1 (polygon) avec label 'frontwall'"
```

## 🚀 **Workflow Final Optimisé**

### **Préparation**
```
1. Analyser l'image pour identifier les zones d'intérêt
2. Déterminer les labels appropriés pour chaque zone
3. Planifier l'ordre de création des ROI
```

### **Création des ROI**
```
1. Sélectionner label 'frontwall' → Créer ROI zone A
2. Sélectionner label 'flaw' → Créer ROI zone B  
3. Sélectionner label 'indication' → Créer ROI zone C
   → 3 ROI sauvegardées avec labels respectifs
```

### **Traitement en Série**
```
Pour chaque image :
1. Charger image → 3 ROI restaurées automatiquement
2. [Recalculer ROI] → Calcul avec labels respectifs
3. Vérifier résultats → Ajuster paramètres si nécessaire
4. Valider → Polygones ajoutés aux bons labels
5. Export automatique → Masques séparés par label
```

## 🎉 **Résultat Final**

### **Système Complet**
```
✅ ROI persistantes entre images
✅ Labels sauvegardés avec chaque ROI
✅ Recalcul avec label correct automatiquement
✅ Polygones ajoutés au bon label
✅ Interface claire avec information label
✅ Workflow organisé et efficace
```

### **Bénéfices**
```
🏷️ Organisation parfaite par type de défaut
⚡ Productivité maximale avec automation
🎯 Précision garantie sans erreur de label
🔧 Flexibilité totale pour différents workflows
📊 Export structuré et professionnel
```

**Les ROI sont maintenant parfaitement liées aux labels et persistent avec leur association ! Workflow professionnel complet pour l'inspection multi-labels !** 🚀
