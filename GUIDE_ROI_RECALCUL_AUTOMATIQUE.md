# 🎯 Guide : ROI Persistantes avec Recalcul Automatique

## 🚀 **Nouveau Comportement Implémenté**

### **Principe de Fonctionnement**
Quand vous changez d'image, les **zones ROI persistantes** :
1. **Restent visibles** (en vert pointillé)
2. **Recalculent automatiquement** tout (threshold + polygones)
3. **Comme si vous veniez de dessiner** la zone sur la nouvelle image
4. **Résultats en mode temporaire** → Vous validez avec Enter ou "Ajouter au label"

## 🔄 **Workflow Complet**

### **Première Image**
```
1. Dessiner une zone ROI (polygone ou rectangle)
   → Zone sauvegardée automatiquement
   → Calcul des polygones dans la zone
   → Résultats en mode temporaire (gris pointillé)

2. Appuyer sur Enter ou "Ajouter au label"
   → Polygones ajoutés au label actuel
   → Export automatique des masques
```

### **Images Suivantes**
```
1. Charger nouvelle image
   → ROI automatiquement restaurée (vert pointillé)
   → RECALCUL AUTOMATIQUE complet :
     • Threshold recalculé (si auto-threshold activé)
     • Nouveaux polygones détectés dans la zone
     • Résultats en mode temporaire (gris pointillé)

2. Vérifier les résultats

3. Appuyer sur Enter ou "Ajouter au label"
   → Validation et export automatique
```

## 🎯 **Avantages du Système**

### **⚡ Automatisation Complète**
- **Pas de re-dessin** des zones
- **Recalcul automatique** adapté à chaque image
- **Threshold dynamique** selon le contenu de l'image
- **Détection adaptative** des défauts

### **🎯 Précision Optimale**
- **Zone identique** sur toutes les images
- **Threshold adapté** au contenu spécifique de chaque image
- **Détection optimisée** pour chaque contexte
- **Résultats cohérents** mais adaptés

### **🔧 Contrôle Utilisateur**
- **Validation manuelle** : Enter ou bouton pour confirmer
- **Vérification visuelle** : Résultats temporaires visibles
- **Ajustements possibles** : Paramètres modifiables avant validation
- **Suppression facile** : Bouton "Supprimer ROI"

## 🔧 **Détails Techniques**

### **Recalcul du Threshold**
```python
# Si auto-threshold activé dans les paramètres de la ROI
if auto_threshold:
    # Recalcul pour la nouvelle image
    threshold = calculate_auto_threshold(
        gray_image, 
        roi_points, 
        percentage, 
        max_threshold, 
        method
    )
    # Mise à jour automatique de l'affichage
    interface.threshold_var.set(threshold)
```

### **Conservation des Paramètres**
```python
# Paramètres sauvegardés avec chaque ROI
roi_parameters = {
    'auto_threshold': True,           # Recalcul automatique
    'threshold_method': 'sauvola',    # Méthode de calcul
    'threshold_percentage': 20,       # Pourcentage de tolérance
    'max_threshold': 254,            # Seuil maximum
    'mask_type': 'polygon',          # Type de masque
    'vertical_gap': 5,               # Distance verticale
    # ... tous les autres paramètres
}
```

### **Processus de Restauration**
```
Pour chaque ROI persistante :
1. Vérifier que les points sont dans l'image
2. Recalculer le threshold (si auto-threshold)
3. Appliquer le threshold dans la zone
4. Détecter les contours
5. Fusionner les contours proches
6. Créer les polygones temporaires
7. Afficher les résultats
```

## 🎨 **Interface Utilisateur**

### **États Visuels**
```
Éléments affichés :
• ROI persistantes : Lignes vertes pointillées
• Polygones temporaires : Lignes grises pointillées  
• Polygones validés : Lignes colorées par label
• Information ROI : "2 ROI persistante(s) (ROI 1 active)"
```

### **Actions Disponibles**
```
Boutons :
• [Supprimer ROI] : Efface toutes les zones persistantes
• [Ajouter au label] : Valide les polygones temporaires
• [Export Masks] : Exporte les masques

Raccourcis :
• Enter : Ajouter au label + Export automatique
• Espace : Changer de label
```

## 📊 **Exemples d'Usage**

### **Cas 1 : Inspection de Série avec Threshold Fixe**
```
Configuration :
• Auto-threshold : DÉSACTIVÉ
• Threshold : 150 (fixe)
• Méthode : percentile

Comportement :
• Image 1 : Threshold 150 → Détection dans la zone
• Image 2 : Threshold 150 → Détection dans la zone
• Image 3 : Threshold 150 → Détection dans la zone
→ Threshold constant, détection cohérente
```

### **Cas 2 : Inspection avec Threshold Adaptatif**
```
Configuration :
• Auto-threshold : ACTIVÉ
• Méthode : sauvola (locale)
• Pourcentage : 20%

Comportement :
• Image 1 : Threshold 145 → Détection optimisée
• Image 2 : Threshold 162 → Détection adaptée
• Image 3 : Threshold 138 → Détection ajustée
→ Threshold adaptatif, détection optimale pour chaque image
```

### **Cas 3 : Zones Multiples**
```
Configuration :
• 3 ROI persistantes
• Paramètres différents par zone
• Auto-threshold sur certaines zones

Comportement :
• Changement d'image → 3 zones recalculées
• Chaque zone avec ses propres paramètres
• Résultats temporaires pour toutes les zones
• Validation globale avec Enter
```

## 🎯 **Workflow Optimisé**

### **Configuration Initiale (Image 1)**
```
1. Charger la première image
2. Dessiner la/les zone(s) d'intérêt
3. Configurer les paramètres :
   • Activer auto-threshold si souhaité
   • Choisir la méthode (sauvola recommandée)
   • Ajuster le pourcentage de tolérance
4. Valider avec Enter
```

### **Traitement en Série (Images 2+)**
```
1. Charger l'image suivante
   → Recalcul automatique dans les zones
2. Vérifier visuellement les résultats
3. Ajuster les paramètres si nécessaire
4. Valider avec Enter
5. Répéter pour l'image suivante
```

## 🚨 **Points d'Attention**

### **Threshold Adaptatif**
- **Recommandé** : Pour images avec variations d'illumination
- **Méthode Sauvola** : Généralement la plus robuste
- **Pourcentage 15-25%** : Bon équilibre sensibilité/robustesse

### **Threshold Fixe**
- **Recommandé** : Pour images homogènes et cohérentes
- **Valeur optimale** : À déterminer sur la première image
- **Contrôle total** : Résultats prévisibles

### **Validation des Résultats**
- **Vérification visuelle** : Toujours contrôler avant validation
- **Ajustements possibles** : Paramètres modifiables en temps réel
- **Annulation facile** : Supprimer ROI si problème

## 🎉 **Bénéfices du Système**

### **🚀 Productivité**
- **90% de temps économisé** sur le re-dessin
- **Traitement automatique** lors du changement d'image
- **Validation rapide** avec Enter
- **Workflow fluide** et intuitif

### **🎯 Qualité**
- **Zones exactement identiques** sur toutes les images
- **Détection adaptée** au contenu de chaque image
- **Threshold optimal** pour chaque contexte
- **Résultats reproductibles** et cohérents

### **🔧 Flexibilité**
- **Paramètres ajustables** en temps réel
- **Zones multiples** avec paramètres différents
- **Threshold fixe ou adaptatif** selon les besoins
- **Contrôle total** sur la validation

## 🎯 **Résumé**

**Le système de ROI persistantes avec recalcul automatique transforme l'application en outil professionnel d'inspection de série :**

✅ **Dessinez une fois** → **Appliquez partout**
✅ **Recalcul automatique** adapté à chaque image  
✅ **Validation manuelle** pour le contrôle qualité
✅ **Workflow optimisé** pour l'inspection industrielle

**Parfait pour analyser des séries d'images avec zones d'intérêt récurrentes et conditions variables !** 🚀
