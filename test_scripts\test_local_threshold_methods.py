#!/usr/bin/env python3
"""
Script de test pour les méthodes de threshold locales corrigées.
Teste adaptive, sauvola et niblack avec calculs vraiment locaux.
"""

import sys
import os
import numpy as np
import cv2
import logging

# Ajouter le répertoire parent au path pour les imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.helpers import calculate_auto_threshold

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def create_gradient_test_image():
    """Crée une image de test avec gradient d'illumination."""
    # Image 300x400 avec gradient d'illumination
    image = np.zeros((300, 400, 3), dtype=np.uint8)
    
    # Créer un gradient horizontal (illumination variable)
    for x in range(400):
        intensity = int(80 + (x / 400) * 100)  # De 80 à 180
        image[:, x] = [intensity, intensity, intensity]
    
    # Ajouter des défauts sombres à différents endroits
    # Zone sombre à gauche (illumination faible)
    cv2.rectangle(image, (50, 50), (100, 100), (30, 30, 30), -1)
    cv2.circle(image, (75, 150), 15, (25, 25, 25), -1)
    
    # Zone sombre au centre (illumination moyenne)
    cv2.rectangle(image, (180, 80), (220, 120), (40, 40, 40), -1)
    cv2.circle(image, (200, 180), 12, (35, 35, 35), -1)
    
    # Zone sombre à droite (illumination forte)
    cv2.rectangle(image, (320, 60), (360, 100), (60, 60, 60), -1)
    cv2.circle(image, (340, 150), 10, (55, 55, 55), -1)
    
    # Ajouter du bruit
    noise = np.random.randint(-5, 5, image.shape, dtype=np.int16)
    image = np.clip(image.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    return image

def create_test_polygon():
    """Crée un polygone de test qui englobe toute l'image."""
    return [(10, 10), (390, 10), (390, 290), (10, 290), (10, 10)]

def test_local_methods():
    """Teste les méthodes locales corrigées."""
    logger.info("=== Test des Méthodes Locales Corrigées ===")
    
    # Créer l'image de test avec gradient
    test_image = create_gradient_test_image()
    test_polygon = create_test_polygon()
    
    # Convertir en niveaux de gris
    gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
    
    logger.info("Image de test créée avec gradient d'illumination")
    logger.info(f"Intensité min: {np.min(gray)}, max: {np.max(gray)}, moyenne: {np.mean(gray):.1f}")
    
    # Tester les méthodes locales
    methods_to_test = ["adaptive", "sauvola", "niblack"]
    results = {}
    
    for method in methods_to_test:
        logger.info(f"\n--- Test Méthode {method.upper()} ---")
        
        try:
            # Calculer le threshold avec la méthode locale
            threshold = calculate_auto_threshold(gray, test_polygon, method=method, percentage=20)
            logger.info(f"Threshold calculé: {threshold}")
            
            # Appliquer le threshold pour voir le résultat
            binary_mask = (gray < threshold).astype(np.uint8) * 255
            
            # Compter les pixels détectés
            detected_pixels = np.sum(binary_mask > 0)
            total_pixels = gray.size
            detection_rate = (detected_pixels / total_pixels) * 100
            
            logger.info(f"Pixels détectés: {detected_pixels} / {total_pixels} ({detection_rate:.1f}%)")
            
            # Analyser la distribution des détections
            # Diviser l'image en 3 zones (gauche, centre, droite)
            w = gray.shape[1]
            left_zone = binary_mask[:, :w//3]
            center_zone = binary_mask[:, w//3:2*w//3]
            right_zone = binary_mask[:, 2*w//3:]
            
            left_rate = (np.sum(left_zone > 0) / left_zone.size) * 100
            center_rate = (np.sum(center_zone > 0) / center_zone.size) * 100
            right_rate = (np.sum(right_zone > 0) / right_zone.size) * 100
            
            logger.info(f"Détection par zone - Gauche: {left_rate:.1f}%, Centre: {center_rate:.1f}%, Droite: {right_rate:.1f}%")
            
            results[method] = {
                "threshold": threshold,
                "detection_rate": detection_rate,
                "zone_rates": [left_rate, center_rate, right_rate],
                "binary_mask": binary_mask
            }
            
        except Exception as e:
            logger.error(f"Erreur avec la méthode {method}: {str(e)}")
            results[method] = None
    
    return test_image, results

def compare_with_global_methods():
    """Compare les méthodes locales avec les méthodes globales."""
    logger.info("\n=== Comparaison Méthodes Locales vs Globales ===")
    
    test_image = create_gradient_test_image()
    test_polygon = create_test_polygon()
    gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
    
    # Méthodes à comparer
    local_methods = ["adaptive", "sauvola", "niblack"]
    global_methods = ["percentile", "otsu", "robust_z"]
    
    logger.info("Méthode          | Threshold | Détection% | Gauche% | Centre% | Droite%")
    logger.info("-" * 70)
    
    for method in local_methods + global_methods:
        try:
            threshold = calculate_auto_threshold(gray, test_polygon, method=method, percentage=20)
            binary_mask = (gray < threshold).astype(np.uint8) * 255
            
            detection_rate = (np.sum(binary_mask > 0) / binary_mask.size) * 100
            
            # Analyser par zones
            w = gray.shape[1]
            left_rate = (np.sum(binary_mask[:, :w//3] > 0) / (binary_mask.shape[0] * w//3)) * 100
            center_rate = (np.sum(binary_mask[:, w//3:2*w//3] > 0) / (binary_mask.shape[0] * w//3)) * 100
            right_rate = (np.sum(binary_mask[:, 2*w//3:] > 0) / (binary_mask.shape[0] * (w - 2*w//3))) * 100
            
            method_type = "LOCAL " if method in local_methods else "GLOBAL"
            logger.info(f"{method_type:6} {method:8} | {threshold:9} | {detection_rate:8.1f}% | {left_rate:6.1f}% | {center_rate:6.1f}% | {right_rate:6.1f}%")
            
        except Exception as e:
            logger.error(f"Erreur avec {method}: {str(e)}")

def save_comparison_image(image, results, output_path="test_local_methods_comparison.png"):
    """Sauvegarde une image de comparaison des méthodes locales."""
    if not results or len([r for r in results.values() if r is not None]) == 0:
        logger.warning("Aucun résultat valide pour la comparaison")
        return
    
    h, w = image.shape[:2]
    valid_results = {k: v for k, v in results.items() if v is not None}
    num_methods = len(valid_results)
    
    if num_methods == 0:
        return
    
    # Créer une image de comparaison
    comparison = np.zeros((h * 2, w * num_methods, 3), dtype=np.uint8)
    
    # Image originale en haut
    for i in range(num_methods):
        comparison[0:h, i*w:(i+1)*w] = image
    
    # Résultats des méthodes en bas
    for i, (method, result) in enumerate(valid_results.items()):
        # Créer une image colorée pour le masque binaire
        mask_colored = np.zeros((h, w, 3), dtype=np.uint8)
        mask_colored[result["binary_mask"] > 0] = [0, 255, 0]  # Vert pour les détections
        
        # Superposer sur l'image originale
        overlay = cv2.addWeighted(image, 0.7, mask_colored, 0.3, 0)
        comparison[h:2*h, i*w:(i+1)*w] = overlay
        
        # Ajouter le nom de la méthode
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(comparison, method.upper(), (i*w + 10, 30), font, 0.8, (255, 255, 255), 2)
        cv2.putText(comparison, f"T={result['threshold']}", (i*w + 10, h + 30), font, 0.6, (255, 255, 255), 2)
        cv2.putText(comparison, f"{result['detection_rate']:.1f}%", (i*w + 10, h + 60), font, 0.6, (255, 255, 255), 2)
    
    cv2.imwrite(output_path, comparison)
    logger.info(f"Image de comparaison sauvegardée: {output_path}")

def main():
    """Fonction principale de test."""
    logger.info("Démarrage des tests des méthodes locales corrigées")
    
    try:
        # Tester les méthodes locales
        test_image, results = test_local_methods()
        
        # Comparaison avec les méthodes globales
        compare_with_global_methods()
        
        # Sauvegarder l'image de comparaison
        save_comparison_image(test_image, results)
        
        logger.info("\n=== Résumé ===")
        logger.info("✅ Tests des méthodes locales terminés")
        
        # Vérifier que les méthodes locales s'adaptent bien au gradient
        valid_results = {k: v for k, v in results.items() if v is not None}
        if valid_results:
            logger.info("\n=== Analyse de l'Adaptation Locale ===")
            for method, result in valid_results.items():
                zones = result["zone_rates"]
                variation = max(zones) - min(zones)
                logger.info(f"{method}: Variation entre zones = {variation:.1f}% (plus c'est élevé, mieux c'est adapté)")
        
        logger.info("✅ Les méthodes locales sont maintenant vraiment locales !")
        
    except Exception as e:
        logger.error(f"Erreur lors des tests: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
