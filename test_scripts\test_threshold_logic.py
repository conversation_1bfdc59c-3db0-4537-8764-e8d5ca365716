#!/usr/bin/env python3
"""
Test spécifique pour vérifier la logique de comparaison <= vs <
"""

import numpy as np
import cv2
from utils.helpers import calculate_auto_threshold

def test_threshold_logic():
    """Test que le pourcentage 0% inclut bien le pixel le plus foncé"""
    print("=== Test de la logique de comparaison ===")
    
    # Créer une image de test simple
    # 3x3 avec un seul pixel noir (valeur 1) au centre
    test_image = np.full((3, 3), 255, dtype=np.uint8)  # Tout blanc
    test_image[1, 1] = 1  # Un pixel noir au centre
    
    print(f"Image de test 3x3:")
    print(test_image)
    print(f"Pixel le plus foncé: {test_image.min()}")
    
    # Définir un polygone qui couvre toute l'image
    polygon_points = [(0, 0), (3, 0), (3, 3), (0, 3), (0, 0)]
    
    # Test avec pourcentage 0%
    threshold_0 = calculate_auto_threshold(test_image, polygon_points, 0)
    print(f"\nPourcentage 0% -> Threshold calculé: {threshold_0}")
    
    # Simuler la logique de masque avec <= (nouvelle logique)
    binary_mask_new = (test_image <= threshold_0).astype(np.uint8) * 255
    pixels_captured_new = np.sum(binary_mask_new == 255)
    
    # Simuler la logique de masque avec < (ancienne logique)
    binary_mask_old = (test_image < threshold_0).astype(np.uint8) * 255
    pixels_captured_old = np.sum(binary_mask_old == 255)
    
    print(f"\nRésultats avec threshold = {threshold_0}:")
    print(f"Logique '<'  (ancienne): {pixels_captured_old} pixels capturés")
    print(f"Logique '<=' (nouvelle): {pixels_captured_new} pixels capturés")
    
    print(f"\nMasque avec '<=' (devrait capturer le pixel noir):")
    print(binary_mask_new)
    
    # Test avec pourcentage 10%
    threshold_10 = calculate_auto_threshold(test_image, polygon_points, 10)
    print(f"\nPourcentage 10% -> Threshold calculé: {threshold_10}")
    
    binary_mask_10 = (test_image <= threshold_10).astype(np.uint8) * 255
    pixels_captured_10 = np.sum(binary_mask_10 == 255)
    print(f"Pixels capturés avec 10%: {pixels_captured_10}")
    
    # Vérifications
    print(f"\n=== Vérifications ===")
    if pixels_captured_new >= 1:
        print("✅ SUCCÈS: Le pourcentage 0% capture au moins le pixel le plus foncé")
    else:
        print("❌ ÉCHEC: Le pourcentage 0% ne capture aucun pixel")
    
    if pixels_captured_new > pixels_captured_old:
        print("✅ SUCCÈS: La nouvelle logique '<=' capture plus de pixels que l'ancienne '<'")
    else:
        print("❌ ÉCHEC: La nouvelle logique ne fonctionne pas mieux que l'ancienne")

if __name__ == "__main__":
    test_threshold_logic()
