# Création de Volume à partir de PNGs

## Description

Cette fonctionnalité permet de convertir un dossier d'images PNG en un volume 3D dans différents formats (NPZ, H5, NIfTI). Elle fusionne les fonctionnalités des scripts `pngs_to_volume.py` et `convertir_volume_pour_sentinel.py`.

## Utilisation

### Via l'interface graphique

1. Lancez l'application : `python __main__.py`
2. Dans le menu **Fichier**, cliquez sur **"Créer un volume"**
3. Sélectionnez le dossier contenant les images PNG
4. Sélectionnez le dossier de sortie
5. Choisissez le format de sortie (npz, h5, ou nifti)
6. La conversion se lance automatiquement

### Formats supportés

- **NPZ** (recommandé) : Format NumPy compressé
  - Conversion automatique au format Sentinel (clé 'arr_0')
  - Fichier de sortie : `nom_dossier.npz`
  
- **H5** : Format HDF5
  - Compression gzip automatique
  - Fichier de sortie : `nom_dossier.h5`
  
- **NIfTI** : Format médical standard
  - Fichier de sortie : `nom_dossier.nii.gz`

### Paramètres par défaut

- **Mode couleur** : Grayscale (L)
- **Ordre des axes** : ZYX (profondeur, hauteur, largeur)
- **Compression** : Activée pour tous les formats
- **Conversion Sentinel** : Automatique pour les fichiers NPZ

## Structure du volume créé

Le volume 3D résultant a la structure suivante :
- **Axe Z** : Nombre d'images PNG (profondeur)
- **Axe Y** : Hauteur des images
- **Axe X** : Largeur des images

Exemple : 100 images de 512x512 pixels → Volume (100, 512, 512)

## Fonctionnalités techniques

### Service fusionné

Le service `services/pngs_to_volume.py` combine :
- Conversion PNG vers volume 3D
- Conversion automatique au format Sentinel pour NPZ
- Support de multiples formats de sortie
- Gestion d'erreurs robuste

### Interface utilisateur

- Boîtes de dialogue intuitives pour sélection des dossiers
- Validation des entrées (présence de fichiers PNG)
- Messages de progression et de confirmation
- Gestion d'erreurs avec messages explicites

## Exemple d'utilisation

```python
from services.pngs_to_volume import convert_pngs_to_volume_gui

# Conversion programmatique
success = convert_pngs_to_volume_gui(
    png_folder="/path/to/pngs",
    output_folder="/path/to/output",
    output_format="npz",
    color_mode="L",
    axis_order="ZYX",
    convert_to_sentinel=True
)
```

## Notes importantes

- Les images PNG sont triées par nom de fichier avant traitement
- Le nom du fichier de sortie est basé sur le nom du dossier d'entrée
- Pour le format NPZ, la conversion Sentinel est automatique (clé 'volume' → 'arr_0')
- La progression est affichée pendant le traitement des gros volumes

## Dépendances

- `numpy` : Manipulation des arrays
- `PIL` (Pillow) : Lecture des images PNG
- `h5py` : Support du format H5 (optionnel)
- `nibabel` : Support du format NIfTI (optionnel)
