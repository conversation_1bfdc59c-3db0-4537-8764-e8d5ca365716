# 🧪 Test : ROI Persistantes - Guide de Vérification

## 🎯 **Objectif du Test**

Vérifier que les **ROI restent actives** après validation et **se réappliquent automatiquement** sur les images suivantes.

## 📋 **Procédure de Test**

### **Étape 1 : Préparation**
```
1. Lancer l'application : python __main__.py outputs/endview_000000000000.png
2. Vérifier que l'interface s'affiche correctement
3. Noter l'état initial : "Aucune ROI active"
```

### **Étape 2 : Création de la ROI**
```
1. Dessiner une zone ROI (polygone ou rectangle)
   → Vérifier message console : "💾 ROI SAUVEGARDÉE: Zone [type] conservée pour les prochaines images"
   → Vérifier interface : "1 ROI persistante(s) (ROI 1 active)"
   → Vérifier affichage : Zone verte pointillée visible

2. Observer les résultats temporaires
   → Polygones gris pointillés dans la zone
   → Boutons "Ajouter au label" et "Export Masks" activés
```

### **Étape 3 : Validation**
```
1. Appuyer sur Enter ou cliquer "Ajouter au label"
   → Polygones ajoutés au label actuel
   → Export automatique des masques
   → ROI reste visible (vert pointillé)
   → Interface : "1 ROI persistante(s) (ROI 1 active)" (DOIT RESTER)
```

### **Étape 4 : Changement d'Image**
```
1. Charger une nouvelle image (Fichier > Ouvrir un dossier)
   → Vérifier message console : "🎯 ROI RESTAURÉES: 1 zone(s) recalculée(s) automatiquement"
   → Vérifier affichage : Zone verte pointillée restaurée
   → Vérifier résultats : Nouveaux polygones gris pointillés
   → Interface : "1 ROI persistante(s) (ROI 1 active)"
```

### **Étape 5 : Validation Continue**
```
1. Appuyer sur Enter pour valider la nouvelle image
   → Polygones ajoutés au label
   → Export automatique
   → ROI reste active pour l'image suivante

2. Répéter avec une 3ème image
   → Même comportement attendu
```

## ✅ **Résultats Attendus**

### **Comportement Correct**
```
✅ ROI sauvegardée lors du premier dessin
✅ ROI reste visible après validation (vert pointillé)
✅ ROI se restaure automatiquement sur nouvelle image
✅ Recalcul automatique des polygones
✅ Threshold recalculé (si auto-threshold activé)
✅ Résultats en mode temporaire (gris pointillé)
✅ Validation possible avec Enter
✅ ROI reste active pour l'image suivante
```

### **Messages Console Attendus**
```
💾 ROI SAUVEGARDÉE: Zone polygon conservée pour les prochaines images
🎯 ROI RESTAURÉES: 1 zone(s) recalculée(s) automatiquement
✅ 1 ROI persistantes restaurées et recalculées pour la nouvelle image
```

### **Interface Attendue**
```
État initial : "Aucune ROI active"
Après création : "1 ROI persistante(s) (ROI 1 active)"
Après validation : "1 ROI persistante(s) (ROI 1 active)" (RESTE)
Nouvelle image : "1 ROI persistante(s) (ROI 1 active)" (RESTE)
```

## 🚨 **Problèmes Possibles**

### **Problème 1 : ROI Disparaît Après Validation**
```
Symptôme : Interface affiche "Aucune ROI active" après Enter
Cause : ROI effacée lors de la validation
Solution : Vérifier que _commit_temporary_polygons ne supprime pas les ROI
```

### **Problème 2 : ROI Ne Se Restaure Pas**
```
Symptôme : Pas de message "🎯 ROI RESTAURÉES" lors du changement d'image
Cause : _restore_persistent_rois pas appelée ou ROI vides
Solution : Vérifier load_image et _persistent_rois
```

### **Problème 3 : Pas de Recalcul**
```
Symptôme : Pas de nouveaux polygones sur la nouvelle image
Cause : Threshold ou détection échoue
Solution : Vérifier calculate_auto_threshold et contours
```

### **Problème 4 : Threshold Pas Recalculé**
```
Symptôme : Même threshold sur toutes les images
Cause : auto_threshold désactivé ou méthode échoue
Solution : Activer auto-threshold et vérifier la méthode
```

## 🔧 **Débogage**

### **Vérifications Console**
```
1. Messages de sauvegarde ROI
2. Messages de restauration ROI
3. Valeurs de threshold calculées
4. Nombre de contours détectés
5. Erreurs éventuelles
```

### **Vérifications Interface**
```
1. État du label ROI info
2. Couleur et visibilité des zones
3. Présence des polygones temporaires
4. État des boutons
```

### **Vérifications Fonctionnelles**
```
1. ROI visible en vert pointillé
2. Polygones temporaires en gris pointillé
3. Boutons activés/désactivés correctement
4. Validation fonctionne
5. Export automatique
```

## 🎯 **Cas de Test Spécifiques**

### **Test 1 : ROI Rectangle**
```
1. Dessiner un rectangle
2. Valider avec Enter
3. Changer d'image
4. Vérifier restauration automatique
```

### **Test 2 : ROI Polygone**
```
1. Dessiner un polygone (5+ points)
2. Valider avec Enter
3. Changer d'image
4. Vérifier restauration automatique
```

### **Test 3 : Auto-Threshold Activé**
```
1. Activer auto-threshold
2. Choisir méthode Sauvola
3. Dessiner ROI et valider
4. Changer d'image
5. Vérifier threshold recalculé
```

### **Test 4 : Auto-Threshold Désactivé**
```
1. Désactiver auto-threshold
2. Fixer threshold à 150
3. Dessiner ROI et valider
4. Changer d'image
5. Vérifier threshold reste 150
```

### **Test 5 : ROI Multiples**
```
1. Dessiner 2 ROI différentes
2. Valider avec Enter
3. Changer d'image
4. Vérifier que les 2 ROI se restaurent
```

### **Test 6 : Suppression ROI**
```
1. Créer ROI et valider
2. Cliquer "Supprimer ROI"
3. Changer d'image
4. Vérifier qu'aucune ROI ne se restaure
```

## 📊 **Critères de Réussite**

### **✅ Test Réussi Si :**
```
1. ROI sauvegardée automatiquement lors du dessin
2. ROI reste visible après validation
3. ROI se restaure sur nouvelle image
4. Recalcul automatique fonctionne
5. Threshold adaptatif fonctionne (si activé)
6. Validation continue possible
7. Messages console corrects
8. Interface cohérente
```

### **❌ Test Échoué Si :**
```
1. ROI disparaît après validation
2. Pas de restauration sur nouvelle image
3. Pas de recalcul automatique
4. Threshold pas adaptatif
5. Erreurs console
6. Interface incohérente
7. Boutons non fonctionnels
```

## 🎉 **Validation Finale**

### **Workflow Complet Testé**
```
Image 1 : Dessiner ROI → Valider → ROI reste active
Image 2 : Charger → ROI restaurée → Recalcul auto → Valider → ROI reste active  
Image 3 : Charger → ROI restaurée → Recalcul auto → Valider → ROI reste active
...
```

### **Gain de Productivité Confirmé**
```
✅ Pas de re-dessin nécessaire
✅ Recalcul automatique adaptatif
✅ Validation simple et rapide
✅ Workflow fluide et efficace
```

**Si tous les tests passent, les ROI persistantes fonctionnent parfaitement ! 🚀**
