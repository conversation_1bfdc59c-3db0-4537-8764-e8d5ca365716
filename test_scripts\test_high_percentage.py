#!/usr/bin/env python3
"""
Test pour vérifier que les pourcentages élevés (jusqu'à 500%) fonctionnent correctement
"""

import numpy as np
from utils.helpers import calculate_auto_threshold

def test_high_percentage():
    print("=== Test des pourcentages élevés (jusqu'à 500%) ===")
    
    # Créer une image de test avec des pixels bien espacés
    test_image = np.full((10, 10), 255, dtype=np.uint8)  # Tout blanc
    test_image[5, 4] = 10   # Premier pixel (le plus foncé)
    test_image[5, 5] = 20   # Deuxième pixel
    test_image[5, 6] = 30   # Troisième pixel (base de calcul)
    
    polygon_points = [(0, 0), (10, 0), (10, 10), (0, 10), (0, 0)]
    
    # Test avec différents pourcentages élevés
    percentages = [0, 50, 100, 200, 300, 400, 500]
    
    print("\nTests avec différents pourcentages:")
    print("3ème pixel le plus foncé: 30")
    print("Formule: Threshold = 30 + (pourcentage/100 × 30)")
    print()
    
    results = {}
    for percentage in percentages:
        threshold = calculate_auto_threshold(test_image, polygon_points, percentage)
        expected = 30 + (percentage / 100.0) * 30
        results[percentage] = threshold
        
        print(f"Pourcentage: {percentage:3d}% -> Calcul: 30 + {percentage/100:.1f}×30 = {expected:5.1f} -> Threshold: {threshold}")
    
    # Test avec threshold maximum pour limiter les pourcentages élevés
    print("\n=== Test avec threshold maximum (max=100) ===")
    print("Même calcul mais plafonné à 100:")
    print()
    
    for percentage in percentages:
        threshold = calculate_auto_threshold(test_image, polygon_points, percentage, max_threshold=100)
        expected = min(100, 30 + (percentage / 100.0) * 30)
        
        print(f"Pourcentage: {percentage:3d}% -> Threshold plafonné: {threshold} (attendu: {expected:.1f})")
    
    # Test de vérification des pixels capturés avec pourcentages élevés
    print("\n=== Vérification des pixels capturés ===")
    
    # Créer une image avec plus de variété
    test_image_2 = np.full((10, 10), 255, dtype=np.uint8)
    values = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
    for i, val in enumerate(values):
        test_image_2[5, i] = val
    
    print("Image avec pixels: [10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 255...]")
    print("3ème pixel le plus foncé: 30")
    print()
    
    test_percentages = [0, 100, 300, 500]
    for percentage in test_percentages:
        threshold = calculate_auto_threshold(test_image_2, polygon_points, percentage)
        binary_mask = (test_image_2 <= threshold).astype(np.uint8) * 255
        captured_values = test_image_2[binary_mask == 255]
        unique_captured = sorted(np.unique(captured_values))
        
        print(f"Pourcentage {percentage:3d}% (threshold={threshold:3d}): Pixels capturés = {unique_captured}")
    
    print("\n=== Vérifications ===")
    
    # Vérification 1: Pourcentage 0% donne exactement le 3ème pixel
    if results[0] == 30:
        print("✅ SUCCÈS: Pourcentage 0% = 30 (3ème pixel)")
    else:
        print(f"❌ ÉCHEC: Pourcentage 0% = {results[0]} au lieu de 30")
    
    # Vérification 2: Pourcentage 100% double la valeur
    if results[100] == 60:
        print("✅ SUCCÈS: Pourcentage 100% = 60 (double)")
    else:
        print(f"❌ ÉCHEC: Pourcentage 100% = {results[100]} au lieu de 60")
    
    # Vérification 3: Pourcentage 500% = 6 fois la valeur
    if results[500] == 180:  # 30 + 5*30 = 180
        print("✅ SUCCÈS: Pourcentage 500% = 180 (6 fois la valeur)")
    else:
        print(f"❌ ÉCHEC: Pourcentage 500% = {results[500]} au lieu de 180")
    
    # Vérification 4: Les pourcentages sont croissants
    is_increasing = all(results[percentages[i]] <= results[percentages[i+1]] 
                       for i in range(len(percentages)-1))
    if is_increasing:
        print("✅ SUCCÈS: Les thresholds augmentent avec le pourcentage")
    else:
        print("❌ ÉCHEC: Les thresholds ne sont pas croissants")
    
    # Vérification 5: Plafonnement fonctionne
    threshold_500_capped = calculate_auto_threshold(test_image, polygon_points, 500, max_threshold=100)
    if threshold_500_capped == 100:
        print("✅ SUCCÈS: Plafonnement à 100 fonctionne avec 500%")
    else:
        print(f"❌ ÉCHEC: Plafonnement = {threshold_500_capped} au lieu de 100")
    
    print("\n=== Test terminé ===")

if __name__ == "__main__":
    test_high_percentage()
