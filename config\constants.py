"""
Global constants and mappings.
"""

# Mapping des classes vers leurs valeurs numériques
CLASS_MAP = {
    'frontwall': 1,
    'backwall': 2,
    'flaw': 3,
    'indication': 4
}

# Paramètres par défaut pour chaque label
LABEL_SETTINGS = {
    'frontwall': {'threshold': 128, 'smooth_kernel': 5},
    'backwall':    {'threshold': 128, 'smooth_kernel': 5},
    'flaw':        {'threshold': 128, 'smooth_kernel': 5},
    'indication':  {'threshold': 128, 'smooth_kernel': 5}
}

# Couleurs des labels pour l'interface utilisateur (format hexadécimal)
LABEL_COLORS_HEX = {
    'frontwall': '#0000FF',  # Bleu
    'backwall': '#00FF00',   # Vert
    'flaw': '#FF0000',       # Rouge
    'indication': '#FFA500'  # Orange
}

# Couleurs des masques pour OpenCV (format BGR)
MASK_COLORS_BGR = {
    1: [255, 0, 0],     # Bleu pour frontwall (BGR)
    2: [0, 255, 0],     # Vert pour backwall (BGR)
    3: [0, 0, 255],     # Rouge pour flaw (BGR)
    4: [0, 165, 255]    # Orange pour indication (BGR)
}

# Couleurs des masques pour PIL/export (format RGB)
MASK_COLORS_RGB = {
    1: [0, 0, 255],     # Bleu pour frontwall (RGB)
    2: [0, 255, 0],     # Vert pour backwall (RGB)
    3: [255, 0, 0],     # Rouge pour flaw (RGB)
    4: [255, 165, 0]    # Orange pour indication (RGB)
}

# Couleurs des masques avec alpha pour OpenCV (format BGRA)
MASK_COLORS_BGRA = {
    1: [255, 0, 0, 255],     # Bleu pour frontwall (BGRA)
    2: [0, 255, 0, 255],     # Vert pour backwall (BGRA)
    3: [0, 0, 255, 255],     # Rouge pour flaw (BGRA)
    4: [0, 165, 255, 255]    # Orange pour indication (BGRA)
}
