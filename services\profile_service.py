"""
Service de génération de masques de profil.
"""
import numpy as np

def generate_profile_mask(single_mask: np.ndarray, val: int) -> np.ndarray:
    """
    Generate a vertical profile mask preserving the original contour.
    Vectorized implementation for performance.

    Args:
        single_mask: Masque binaire (0 ou 1) représentant la zone du polygone
        val: Valeur de classe à assigner (1, 2, 3, ou 4)

    Returns:
        Masque avec profil vertical et valeurs de classe correctes
    """
    h, w = single_mask.shape
    profile_mask = np.zeros((h, w), dtype=np.uint8)

    # Boolean mask - chercher les pixels non-nuls (valeur 1) dans le masque binaire
    mask_bool = single_mask > 0

    # Any column with at least one pixel
    any_col = mask_bool.any(axis=0)

    # Compute y_min for each column
    y_min = np.where(any_col, mask_bool.argmax(axis=0), -1)

    # Compute y_max by argmax on reversed rows
    y_max = np.where(any_col,
                     h - 1 - mask_bool[::-1].argmax(axis=0),
                     -1)

    # Fill between y_min and y_max with the correct class value
    cols = np.nonzero(any_col)[0]
    for x in cols:
        profile_mask[y_min[x]:y_max[x]+1, x] = val

    return profile_mask
