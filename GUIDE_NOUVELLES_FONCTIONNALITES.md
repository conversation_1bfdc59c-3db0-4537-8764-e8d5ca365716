# Guide des Nouvelles Fonctionnalités - Mask Editor

## 🎨 Nouveau Layout Horizontal

### Avant vs Après
- **Avant** : Layout vertical (contrôles en haut, image en bas)
- **Après** : Layout horizontal (contrôles à gauche, image à droite)

### Avantages
- ✅ **Meilleure utilisation de l'espace** : L'image peut maintenant utiliser tout l'espace horizontal disponible
- ✅ **Interface plus intuitive** : Contrôles regroupés logiquement à gauche, image principale à droite
- ✅ **Workflow amélioré** : Plus facile de travailler sur l'image tout en ayant accès aux contrôles
- ✅ **Scrollbar automatique** : Les contrôles peuvent défiler si la fenêtre devient trop petite

## 🔧 Nouvelles Méthodes de Threshold Automatique

### Méthodes Disponibles

#### 1. **Percentile** (Recommandé par défaut)
- **Description** : Robuste aux valeurs extrêmes
- **Utilisation** : Bon choix général pour la plupart des images
- **Avantage** : Stable et prévisible

#### 2. **Otsu**
- **Description** : Sépare automatiquement 2 classes d'intensité
- **Utilisation** : Excellent si l'image a 2 zones distinctes (fond vs défaut)
- **Avantage** : Méthode classique très efficace pour images bimodales

#### 3. **Z-score Robuste**
- **Description** : Basé sur médiane et IQR (Interquartile Range)
- **Utilisation** : Très stable avec éclairage variable
- **Avantage** : Résistant aux valeurs aberrantes

#### 4. **Adaptatif**
- **Description** : S'adapte aux variations locales d'illumination
- **Utilisation** : Idéal pour illumination non-uniforme
- **Avantage** : Gère bien les gradients d'éclairage

#### 5. **Hystérésis**
- **Description** : Double seuil avec connectivité
- **Utilisation** : Réduit le bruit, garde la connectivité des défauts
- **Avantage** : Résultats plus nets et connectés

#### 6. **K-means** (Nouveau !)
- **Description** : Clustering en 2 classes (claire/sombre)
- **Utilisation** : Fonctionne même si la zone sombre est grande ou petite
- **Avantage** : Robuste si la distribution est compliquée (pas seulement bimodale)

#### 7. **GMM** (Nouveau !)
- **Description** : Gaussian Mixture Model - clustering probabiliste
- **Utilisation** : Version plus sophistiquée du K-means
- **Avantage** : Gère mieux les distributions complexes et le bruit

#### 8. **Sauvola** (Nouveau !)
- **Description** : Basée sur moyenne et variance locales
- **Utilisation** : Très robuste sur images hétérogènes/bruitées
- **Avantage** : Excellente pour images avec variations d'illumination

#### 9. **Niblack** (Nouveau !)
- **Description** : Binarisation de documents adaptative
- **Utilisation** : Méthode classique pour textes et documents
- **Avantage** : Simple et efficace pour contrastes variables

#### 10. **Moyenne** (Ancienne méthode)
- **Description** : Moyenne min/max dans la zone
- **Utilisation** : Conservée pour compatibilité
- **Note** : Moins robuste que les nouvelles méthodes

#### 11. **3ème pixel** (Ancienne méthode)
- **Description** : 3ème pixel le plus foncé
- **Utilisation** : Conservée pour compatibilité
- **Note** : Très sensible, peut être instable

## 🎛️ Interface Améliorée

### Contrôles de Threshold Automatique
- **Dropdown compact** : Sélection de méthode via menu déroulant
- **Description dynamique** : Explication de la méthode sélectionnée
- **Affichage conditionnel** : Les contrôles n'apparaissent que quand le threshold automatique est activé
- **Layout optimisé** : Paramètres sur une seule ligne (±%, Max)

### Scrollbar Intelligente
- **Scroll automatique** : Défilement avec la molette de la souris
- **Adaptation dynamique** : La zone de scroll s'adapte au contenu
- **Largeur fixe** : Les contrôles gardent une largeur constante de 400px

## 📊 Résultats des Tests

### Performance des Méthodes (sur image de test)
```
Méthode          | Threshold 20% | Threshold 50% | Caractéristiques
-----------------|---------------|---------------|------------------
Percentile       |      21       |      27       | Robuste, stable
Otsu             |      70       |      88       | Bon pour 2 classes
Z-score robuste  |      25       |      31       | Très stable
Adaptatif        |     235       |     254       | Conservateur
Hystérésis       |      34       |      43       | Connectivité
K-means          |     144       |     180       | Clustering robuste
GMM              |     144       |     180       | Clustering probabiliste
Sauvola          |     148       |     185       | Variance locale
Niblack          |     141       |     176       | Adaptatif simple
Moyenne          |     125       |     156       | Ancienne méthode
3ème pixel       |       6       |       7       | Très sensible
```

## 🚀 Utilisation Recommandée

### Pour la plupart des cas
1. **Commencer par "Percentile"** (méthode par défaut)
2. **Ajuster le pourcentage** (±20% est un bon début)
3. **Tester sur quelques images** pour valider

### Pour des cas spécifiques
- **Images avec fond uniforme** → Utiliser "Otsu"
- **Éclairage variable** → Utiliser "Z-score robuste", "Adaptatif", ou "Sauvola"
- **Défauts fins/connectés** → Utiliser "Hystérésis"
- **Distributions complexes** → Utiliser "K-means" ou "GMM"
- **Images très bruitées** → Utiliser "Sauvola" ou "GMM"
- **Documents/textes** → Utiliser "Niblack" ou "Sauvola"
- **Besoin de stabilité** → Utiliser "Percentile" ou "Z-score robuste"

### Paramètres Recommandés
- **Pourcentage** : 15-30% pour la plupart des cas
- **Threshold Max** : 254 (valeur par défaut)
- **Méthode** : "Percentile" pour commencer

## 🔧 Résolution de Problèmes

### Si les contrôles disparaissent
- **Cause** : Fenêtre trop petite
- **Solution** : Utiliser la scrollbar ou agrandir la fenêtre

### Si le threshold semble incorrect
- **Essayer une autre méthode** : Chaque méthode a ses forces
- **Ajuster le pourcentage** : ±10-50% selon le cas
- **Vérifier la zone ROI** : S'assurer qu'elle contient le défaut

### Si l'application est lente
- **Réduire la taille de la zone ROI** : Plus petite = plus rapide
- **Utiliser "Percentile"** : Généralement plus rapide que "Adaptatif"

## 📝 Notes Techniques

### Compatibilité
- ✅ **Rétrocompatible** : Les anciennes méthodes sont conservées
- ✅ **Paramètres sauvegardés** : La méthode sélectionnée est mémorisée
- ✅ **Performance** : Nouvelles méthodes optimisées

### Développement
- **Tests automatiques** : Script `test_scripts/test_new_threshold_methods.py`
- **Logs détaillés** : Toutes les actions sont loggées
- **Code modulaire** : Facile d'ajouter de nouvelles méthodes

---

**Version** : 2.0 avec layout horizontal et méthodes de threshold avancées  
**Date** : Décembre 2024  
**Auteur** : Équipe Mask Editor
