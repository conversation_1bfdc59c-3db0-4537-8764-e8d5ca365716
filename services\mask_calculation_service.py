"""
Service pour le calcul des masques.
Sépare la logique métier de calcul des masques de la vue.
"""
import cv2
import numpy as np
import logging
from typing import Dict, List, Tuple, Any
from config.constants import MASK_COLORS_BGR
from services.profile_service import generate_profile_mask
from utils.morphology import smooth_mask_contours


class MaskCalculationService:
    """Service pour calculer les masques avec différents paramètres."""
    
    def __init__(self):
        self._logger = logging.getLogger(__name__)
    
    def calculate_individual_mask(self, image: np.ndarray, polygons: Dict[str, List], 
                                label_settings: Dict[str, Dict[str, Any]]) -> np.ndarray:
        """
        Calcule le masque coloré avec paramètres individuels pour chaque label.
        
        Args:
            image: Image originale
            polygons: Polygones par label
            label_settings: Paramètres individuels par label
            
        Returns:
            Masque coloré avec valeurs de classe
        """
        if image is None:
            return np.zeros((100, 100), dtype=np.uint8)
        
        h, w = image.shape[:2]
        final_mask = np.zeros((h, w), dtype=np.uint8)
        
        # Traiter chaque label avec ses propres paramètres
        for label, label_value in [("frontwall", 1), ("backwall", 2), ("flaw", 3), ("indication", 4)]:
            if label not in label_settings:
                continue
                
            settings = label_settings[label]
            threshold = settings["threshold"]
            mask_type = settings["mask_type"]
            smooth_contours = settings.get("smooth_contours", False)
            
            # Calculer le masque binaire pour ce seuil
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            binary_mask = (gray < threshold).astype(np.uint8) * 255
            
            # Calculer le masque pour ce label
            if mask_type == "standard":
                label_mask = self._calculate_standard_mask_for_label(
                    binary_mask, h, w, label, polygons[label], label_value
                )
            else:  # polygon
                label_mask = self._calculate_polygon_mask_for_label(
                    binary_mask, h, w, label, polygons[label], label_value, settings
                )
            
            # Appliquer le lissage si activé
            if smooth_contours and np.any(label_mask):
                kernel_size = settings.get("smooth_kernel", 5)
                # Créer un masque binaire temporaire
                temp_binary = (label_mask == label_value).astype(np.uint8) * 255
                smoothed_binary = smooth_mask_contours(temp_binary, kernel_size)
                # Remettre la valeur de classe
                label_mask = np.where(smoothed_binary > 0, label_value, 0).astype(np.uint8)
            
            # Fusionner avec le masque final
            final_mask = np.maximum(final_mask, label_mask)
        
        return final_mask
    
    def _calculate_standard_mask_for_label(self, binary_mask: np.ndarray, h: int, w: int, 
                                         label: str, polygons: list, label_value: int) -> np.ndarray:
        """Calcule le masque standard pour un label spécifique."""
        mask = np.zeros((h, w), dtype=np.uint8)
        
        for pts in polygons:
            if len(pts) >= 3:
                points = np.array(pts, np.int32)
                tmp = np.zeros_like(mask, dtype=np.uint8)
                cv2.fillPoly(tmp, [points], 1)
                # Appliquer le threshold
                tmp &= binary_mask
                # Assigner la valeur de classe
                tmp[tmp > 0] = label_value
                mask = np.maximum(mask, tmp)
        
        return mask
    
    def _calculate_polygon_mask_for_label(self, binary_mask: np.ndarray, h: int, w: int, 
                                        label: str, polygons: list, label_value: int,
                                        settings: Dict[str, Any]) -> np.ndarray:
        """Calcule le masque polygonal pour un label spécifique."""
        mask = np.zeros((h, w), dtype=np.uint8)
        
        for pts in polygons:
            if len(pts) >= 3:
                points = np.array(pts, np.int32)
                tmp = np.zeros_like(mask, dtype=np.uint8)
                cv2.fillPoly(tmp, [points], 1)
                # Appliquer le threshold
                tmp &= binary_mask
                # Générer le masque de profil avec verticalisation pour ce label spécifique
                profile_mask = self._generate_profile_mask_for_label(tmp, label_value, settings)
                mask = np.maximum(mask, profile_mask)
        
        return mask
    
    def _generate_profile_mask_for_label(self, single_mask: np.ndarray, val: int, 
                                       settings: Dict[str, Any]) -> np.ndarray:
        """
        Génère un masque de profil pour un label spécifique avec ses propres paramètres.
        
        Args:
            single_mask: Masque binaire initial
            val: Valeur du label à appliquer
            settings: Paramètres du label
            
        Returns:
            Masque respectant strictement le contour avec paramètres du label
        """
        try:
            mask_type = settings["mask_type"]
            
            # Créer un masque temporaire pour la fusion
            fusion_mask = np.zeros_like(single_mask)
            
            # Trouver les contours du masque binaire
            contours, _ = cv2.findContours(single_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
            
            if not contours:
                return single_mask
            
            # Fusionner tous les contours en un seul
            if len(contours) > 1:
                # Remplir tous les contours dans le masque de fusion
                cv2.drawContours(fusion_mask, contours, -1, 1, -1)
                # Trouver le contour externe de la fusion
                contours, _ = cv2.findContours(fusion_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
            
            # Créer le masque final
            profile_mask = np.zeros_like(single_mask)
            
            # Pour chaque contour (maintenant fusionné)
            for contour in contours:
                # Remplir le contour avec la valeur du label
                cv2.fillPoly(profile_mask, [contour], val)
            
            # Appliquer la verticalisation seulement si ce label est en mode polygon
            if mask_type == "polygon":
                h, w = profile_mask.shape
                # Pour chaque colonne x
                for x in range(w):
                    # Trouver tous les pixels non-nuls dans cette colonne
                    col = profile_mask[:, x]
                    idx = np.where(col == val)[0]
                    if idx.size > 0:
                        # Prendre le plus petit et le plus grand y
                        y_min = idx.min()
                        y_max = idx.max()
                        # Remplir verticalement entre ces points
                        profile_mask[y_min:y_max+1, x] = val
            
            return profile_mask
            
        except Exception as e:
            self._logger.error(f"Erreur lors de la génération du masque de profil: {str(e)}")
            return single_mask
    
    def apply_overlay(self, image: np.ndarray, mask: np.ndarray, alpha: float) -> np.ndarray:
        """
        Applique un overlay coloré sur l'image.
        
        Args:
            image: Image de base
            mask: Masque avec valeurs de classe
            alpha: Transparence (0-100)
            
        Returns:
            Image avec overlay
        """
        if image is None or mask is None:
            return image
        
        # Convertir alpha de 0-100 vers 0-1
        alpha_normalized = alpha / 100.0
        
        # Créer l'overlay coloré
        overlay = np.zeros_like(image)
        
        for class_value, bgr_color in MASK_COLORS_BGR.items():
            class_pixels = (mask == class_value)
            if np.any(class_pixels):
                overlay[class_pixels] = bgr_color
        
        # Créer le masque de transparence
        mask_alpha = (mask > 0).astype(np.float32) * alpha_normalized
        mask_alpha = np.stack([mask_alpha] * 3, axis=-1)
        
        # Appliquer l'overlay
        result = image.astype(np.float32) * (1 - mask_alpha) + overlay.astype(np.float32) * mask_alpha
        
        return result.astype(np.uint8)
