# 🔄 Guide : Bouton "Recalculer ROI"

## 🎯 **Nouveau Bouton Ajouté**

### **Problème Résolu**
- **Problème** : Le calcul ne se fait pas automatiquement lors du changement d'image
- **Solution** : Bouton **"Recalculer ROI"** pour relancer manuellement le calcul sur les zones actives

### **Localisation**
```
Interface → Section "Gestion des ROI" → Boutons :
[Recalculer ROI] [Supprimer ROI]
```

## 🔄 **Fonctionnement du Bouton**

### **Quand l'Utiliser**
```
✅ Après avoir changé d'image (si le calcul ne s'est pas fait automatiquement)
✅ Après avoir modifié les paramètres (threshold, méthode, etc.)
✅ Pour forcer un nouveau calcul sur les zones existantes
✅ Pour actualiser les résultats avec les nouveaux réglages
```

### **Ce Que Fait le Bouton**
```
1. Efface les polygones temporaires existants
2. Relance le calcul complet sur toutes les ROI persistantes :
   ├── Recalcul du threshold (si auto-threshold activé)
   ├── Application du threshold dans chaque zone ROI
   ├── Détection des contours
   ├── Fusion des contours proches
   ├── Création des nouveaux polygones temporaires (gris pointillé)
   ├── Activation des boutons "Ajouter au label"
   └── Mise à jour de l'affichage
3. Affiche les nouveaux résultats en mode temporaire
```

## 🎯 **États du Bouton**

### **Bouton Activé (Vert)**
```
État : [Recalculer ROI] (cliquable)
Condition : Il y a au moins 1 ROI persistante active
Action : Clic → Relance le calcul sur toutes les ROI
```

### **Bouton Désactivé (Grisé)**
```
État : [Recalculer ROI] (non cliquable)
Condition : Aucune ROI persistante active
Message : "⚠️ Aucune ROI active à recalculer"
```

## 🔄 **Workflow Typique**

### **Cas 1 : Changement d'Image Sans Calcul Automatique**
```
1. Charger nouvelle image
2. Observer : ROI visible (vert pointillé) mais pas de polygones temporaires
3. Cliquer [Recalculer ROI]
4. Observer : Nouveaux polygones temporaires (gris pointillé) apparaissent
5. Valider avec Enter ou "Ajouter au label"
```

### **Cas 2 : Modification des Paramètres**
```
1. Modifier threshold, méthode, ou autres paramètres
2. Cliquer [Recalculer ROI]
3. Observer : Polygones recalculés avec nouveaux paramètres
4. Valider si satisfait des résultats
```

### **Cas 3 : Actualisation des Résultats**
```
1. Résultats existants pas satisfaisants
2. Ajuster paramètres (threshold, pourcentage, etc.)
3. Cliquer [Recalculer ROI]
4. Comparer nouveaux résultats
5. Répéter jusqu'à satisfaction
```

## 🎯 **Messages de Confirmation**

### **Console**
```
🔄 Recalcul manuel déclenché pour 2 ROI persistante(s)
🔄 Reproduction de l'événement de création pour ROI 1 (rectangle)
Threshold recalculé pour rectangle: 156
🔄 Reproduction de l'événement de création pour ROI 2 (polygone)
Threshold recalculé pour polygone: 142
✅ 2 ROI persistantes restaurées avec événements complets
```

### **Terminal**
```
🔄 RECALCUL MANUEL: 2 zone(s) recalculée(s)
🎯 ROI RESTAURÉES: 2 zone(s) avec événements complets reproduits
```

### **Si Aucune ROI**
```
Console : "Aucune ROI persistante à recalculer"
Terminal : "⚠️ Aucune ROI active à recalculer"
```

## 🔧 **Avantages du Bouton**

### **🎯 Contrôle Total**
- **Calcul à la demande** : Vous décidez quand recalculer
- **Paramètres en temps réel** : Testez différents réglages instantanément
- **Feedback immédiat** : Voyez les résultats immédiatement
- **Itération rapide** : Ajustez et recalculez facilement

### **⚡ Flexibilité**
- **Pas de calcul automatique forcé** : Évite les calculs non désirés
- **Recalcul sélectif** : Seulement quand vous le voulez
- **Paramètres modifiables** : Changez les réglages avant recalcul
- **Résultats comparables** : Gardez les anciens résultats jusqu'au recalcul

### **🔄 Robustesse**
- **Même logique** : Utilise exactement le même code que la création de ROI
- **Résultats identiques** : Garantie de cohérence
- **Gestion d'erreurs** : Messages clairs en cas de problème
- **État cohérent** : Interface toujours dans le bon état

## 📊 **Exemples d'Usage**

### **Exemple 1 : Threshold Adaptatif**
```
Situation : Auto-threshold activé, méthode Sauvola
Action :
1. Charger nouvelle image
2. Cliquer [Recalculer ROI]
3. Observer threshold recalculé : 145 → 162
4. Nouveaux polygones adaptés à l'image
5. Valider les résultats
```

### **Exemple 2 : Ajustement Manuel**
```
Situation : Threshold fixe trop bas, trop de détections
Action :
1. Augmenter threshold : 120 → 150
2. Cliquer [Recalculer ROI]
3. Observer moins de polygones détectés
4. Ajuster encore si nécessaire : 150 → 140
5. Recalculer et valider
```

### **Exemple 3 : Changement de Méthode**
```
Situation : Méthode Percentile pas optimale
Action :
1. Changer pour méthode Sauvola
2. Cliquer [Recalculer ROI]
3. Observer détection plus précise
4. Comparer avec résultats précédents
5. Valider la nouvelle méthode
```

### **Exemple 4 : ROI Multiples**
```
Situation : 3 ROI avec paramètres différents
Action :
1. Modifier paramètres globaux
2. Cliquer [Recalculer ROI]
3. Observer : Les 3 ROI recalculées simultanément
4. Chaque ROI garde ses paramètres spécifiques
5. Valider toutes les zones
```

## 🎯 **Interface Utilisateur**

### **Boutons ROI**
```
[Recalculer ROI] - Vert, actif si ROI présentes
[Supprimer ROI]  - Rouge, supprime toutes les ROI
```

### **Information ROI**
```
État affiché :
• "Aucune ROI active" (gris) → Bouton Recalculer désactivé
• "2 ROI persistante(s) (ROI 1 active)" (vert) → Bouton Recalculer activé
```

### **Résultats Visuels**
```
Avant recalcul :
• ROI visibles (vert pointillé)
• Pas de polygones temporaires

Après recalcul :
• ROI visibles (vert pointillé)
• Polygones temporaires (gris pointillé)
• Boutons "Ajouter au label" activés
```

## 🚀 **Workflow Optimisé**

### **Workflow Complet avec Bouton**
```
Image 1 :
1. Dessiner ROI → Calcul automatique → Valider

Images suivantes :
1. Charger image → ROI restaurée (vert pointillé)
2. Cliquer [Recalculer ROI] → Calcul complet → Polygones temporaires
3. Ajuster paramètres si nécessaire → Recalculer
4. Valider avec Enter → Export automatique
```

### **Avantages du Workflow**
```
✅ Contrôle total sur le moment du calcul
✅ Possibilité d'ajuster les paramètres avant calcul
✅ Feedback visuel immédiat
✅ Itération rapide pour optimiser les résultats
✅ Pas de calculs automatiques non désirés
```

## 🎯 **Cas d'Usage Spécifiques**

### **Inspection de Série avec Ajustements**
```
1. Créer ROI sur première image
2. Pour chaque image suivante :
   a. Charger image
   b. Cliquer [Recalculer ROI]
   c. Ajuster paramètres si nécessaire
   d. Recalculer jusqu'à satisfaction
   e. Valider et passer à l'image suivante
```

### **Optimisation des Paramètres**
```
1. Charger image de test
2. Créer ROI
3. Tester différents paramètres :
   a. Modifier threshold/méthode
   b. Cliquer [Recalculer ROI]
   c. Observer résultats
   d. Répéter jusqu'à optimisation
4. Appliquer paramètres optimaux sur série
```

### **Contrôle Qualité**
```
1. Traitement automatique d'une série
2. Vérification ponctuelle :
   a. Charger image suspecte
   b. Cliquer [Recalculer ROI]
   c. Vérifier résultats
   d. Ajuster si nécessaire
   e. Continuer le traitement
```

## 🎉 **Résumé**

### **Problème Résolu**
```
❌ Avant : Calcul ne se fait pas automatiquement lors du changement d'image
✅ Après : Bouton [Recalculer ROI] pour relancer le calcul à la demande
```

### **Fonctionnalités**
```
✅ Bouton intelligent (activé/désactivé selon contexte)
✅ Recalcul complet de toutes les ROI persistantes
✅ Même logique que la création de ROI
✅ Messages de confirmation clairs
✅ Interface cohérente et intuitive
```

### **Workflow Final**
```
Dessiner ROI → Valider → Changer image → [Recalculer ROI] → Valider → Répéter
```

**Le bouton "Recalculer ROI" vous donne le contrôle total sur le moment et les conditions du recalcul des zones persistantes !** 🚀
