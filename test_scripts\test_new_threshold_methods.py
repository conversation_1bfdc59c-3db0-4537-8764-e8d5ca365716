#!/usr/bin/env python3
"""
Test des nouvelles méthodes de calcul de threshold automatique.
"""

import numpy as np
import sys
import os

# Ajouter le répertoire parent au path pour importer utils
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.helpers import calculate_auto_threshold

def test_new_threshold_methods():
    print("=== Test des Nouvelles Méthodes de Threshold Automatique ===")
    
    # Créer une image de test avec différents types de zones
    test_image = np.full((100, 100), 200, dtype=np.uint8)  # Fond gris clair
    
    # Zone avec défaut sombre (simulation d'une fissure)
    test_image[40:60, 40:60] = 50  # Zone sombre principale
    test_image[45:55, 45:55] = 20  # Zone très sombre au centre
    test_image[48:52, 48:52] = 5   # Zone extrêmement sombre
    
    # Ajouter du bruit
    noise = np.random.randint(-10, 10, test_image.shape)
    test_image = np.clip(test_image.astype(int) + noise, 0, 255).astype(np.uint8)
    
    # Définir un polygone qui englobe la zone d'intérêt
    polygon_points = [(35, 35), (65, 35), (65, 65), (35, 65), (35, 35)]
    
    print(f"Image de test créée: {test_image.shape}")
    print(f"Zone d'intérêt: polygone {polygon_points}")
    print(f"Valeurs dans la zone: min={np.min(test_image[35:65, 35:65])}, max={np.max(test_image[35:65, 35:65])}")
    print()
    
    # Tester toutes les méthodes
    methods = [
        ("percentile", "Percentile (robuste aux valeurs extrêmes)"),
        ("otsu", "Otsu (sépare automatiquement 2 classes)"),
        ("robust_z", "Z-score robuste (basé sur médiane/IQR)"),
        ("adaptive", "Adaptatif (bon si illumination variable)"),
        ("hysteresis", "Hystérésis (double seuil + connectivité)"),
        ("kmeans", "K-means (clustering en 2 classes claire/sombre)"),
        ("gmm", "GMM (clustering probabiliste, robuste)"),
        ("sauvola", "Sauvola (moyenne + variance locales, très robuste)"),
        ("niblack", "Niblack (binarisation de documents, adaptatif)"),
        ("average", "Moyenne min/max (ancienne méthode)"),
        ("third_darkest", "3ème pixel le plus foncé (ancienne méthode)")
    ]
    
    print("Tests avec différentes méthodes:")
    print("-" * 60)
    
    results = {}
    
    for method, description in methods:
        try:
            # Test avec paramètres par défaut
            threshold_default = calculate_auto_threshold(test_image, polygon_points, 20, 254, method)
            
            # Test avec extension plus élevée
            threshold_high = calculate_auto_threshold(test_image, polygon_points, 50, 254, method)
            
            results[method] = (threshold_default, threshold_high)
            
            print(f"{method:12} | {description}")
            print(f"             | Threshold (20%): {threshold_default:3d}")
            print(f"             | Threshold (50%): {threshold_high:3d}")
            print("-" * 60)
            
        except Exception as e:
            print(f"{method:12} | ERREUR: {str(e)}")
            print("-" * 60)
    
    # Analyse des résultats
    print("\n=== Analyse des Résultats ===")
    
    if results:
        thresholds_20 = [results[method][0] for method in results]
        thresholds_50 = [results[method][1] for method in results]
        
        print(f"Thresholds avec 20% d'extension:")
        print(f"  Min: {min(thresholds_20)}, Max: {max(thresholds_20)}, Moyenne: {np.mean(thresholds_20):.1f}")
        
        print(f"Thresholds avec 50% d'extension:")
        print(f"  Min: {min(thresholds_50)}, Max: {max(thresholds_50)}, Moyenne: {np.mean(thresholds_50):.1f}")
        
        # Recommandations
        print(f"\n=== Recommandations ===")
        print(f"• 'percentile' : Bon choix général, robuste")
        print(f"• 'otsu' : Excellent si l'image a 2 zones distinctes")
        print(f"• 'robust_z' : Très stable avec éclairage variable")
        print(f"• 'adaptive' : Idéal pour illumination non-uniforme")
        print(f"• 'hysteresis' : Réduit le bruit, garde la connectivité")
        
        print(f"\n✅ Test terminé avec succès !")
    else:
        print("❌ Aucun résultat obtenu")

if __name__ == "__main__":
    test_new_threshold_methods()
