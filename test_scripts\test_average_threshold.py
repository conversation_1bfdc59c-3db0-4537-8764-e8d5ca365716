#!/usr/bin/env python3
"""
Test pour vérifier que le threshold automatique utilise la moyenne entre 
le pixel le plus foncé et le pixel le moins foncé
"""

import numpy as np
from utils.helpers import calculate_auto_threshold

def test_average_threshold():
    print("=== Test du threshold basé sur la moyenne min/max ===")
    
    # Test 1: Cas simple avec valeurs bien définies
    print("\n1. Test avec pixels [10, 50, 100, 200]:")
    test_image_1 = np.full((10, 10), 255, dtype=np.uint8)  # Fond blanc
    test_image_1[5, 4] = 10   # Pixel le plus foncé
    test_image_1[5, 5] = 50   # Pixel intermédiaire
    test_image_1[5, 6] = 100  # Pixel intermédiaire
    test_image_1[5, 7] = 200  # Pixel le moins foncé (dans la zone)
    
    polygon_points = [(0, 0), (10, 0), (10, 10), (0, 10), (0, 0)]
    
    threshold_1 = calculate_auto_threshold(test_image_1, polygon_points, 0)  # 0% d'extension
    expected_1 = (10 + 200) / 2  # Moyenne entre min et max
    print(f"Pixel le plus foncé: 10")
    print(f"Pixel le moins foncé: 200")
    print(f"Moyenne attendue: {expected_1}")
    print(f"Threshold calculé: {threshold_1}")
    
    # Test 2: Avec extension de 20%
    print("\n2. Test avec extension 20%:")
    threshold_2 = calculate_auto_threshold(test_image_1, polygon_points, 20)
    expected_base_2 = (10 + 200) / 2  # 105
    expected_extension_2 = expected_base_2 * 0.20  # 21
    expected_final_2 = expected_base_2 + expected_extension_2  # 126
    print(f"Threshold de base: {expected_base_2}")
    print(f"Extension 20%: {expected_extension_2}")
    print(f"Threshold final attendu: {expected_final_2}")
    print(f"Threshold calculé: {threshold_2}")
    
    # Test 3: Cas avec pixels très contrastés
    print("\n3. Test avec pixels très contrastés [1, 254]:")
    test_image_3 = np.full((10, 10), 255, dtype=np.uint8)  # Fond blanc
    test_image_3[5, 4] = 1    # Pixel très foncé
    test_image_3[5, 5] = 254  # Pixel très clair (mais pas blanc)
    
    threshold_3 = calculate_auto_threshold(test_image_3, polygon_points, 0)
    expected_3 = (1 + 254) / 2  # 127.5
    print(f"Pixel le plus foncé: 1")
    print(f"Pixel le moins foncé: 254")
    print(f"Moyenne attendue: {expected_3}")
    print(f"Threshold calculé: {threshold_3}")
    
    # Test 4: Cas avec tous les pixels identiques
    print("\n4. Test avec tous les pixels identiques [100]:")
    test_image_4 = np.full((10, 10), 255, dtype=np.uint8)  # Fond blanc
    test_image_4[5, 4] = 100  # Tous les pixels de la zone ont la même valeur
    test_image_4[5, 5] = 100
    test_image_4[5, 6] = 100
    
    threshold_4 = calculate_auto_threshold(test_image_4, polygon_points, 0)
    expected_4 = (100 + 100) / 2  # 100
    print(f"Tous les pixels: 100")
    print(f"Moyenne attendue: {expected_4}")
    print(f"Threshold calculé: {threshold_4}")
    
    # Test 5: Vérification des pixels capturés
    print("\n5. Vérification des pixels capturés:")
    
    # Utiliser l'image du test 1
    binary_mask = (test_image_1 <= threshold_1).astype(np.uint8) * 255
    captured_values = test_image_1[binary_mask == 255]
    unique_captured = sorted(np.unique(captured_values))
    
    print(f"Avec threshold {threshold_1} (moyenne sans extension):")
    print(f"Pixels capturés: {unique_captured}")
    
    # Avec extension
    binary_mask_ext = (test_image_1 <= threshold_2).astype(np.uint8) * 255
    captured_values_ext = test_image_1[binary_mask_ext == 255]
    unique_captured_ext = sorted(np.unique(captured_values_ext))
    
    print(f"Avec threshold {threshold_2} (moyenne + 20% extension):")
    print(f"Pixels capturés: {unique_captured_ext}")
    
    # Test 6: Test avec plafonnement
    print("\n6. Test avec plafonnement (max_threshold=150):")
    threshold_6 = calculate_auto_threshold(test_image_1, polygon_points, 100, max_threshold=150)
    expected_base_6 = (10 + 200) / 2  # 105
    expected_with_ext_6 = expected_base_6 + (expected_base_6 * 1.0)  # 105 + 105 = 210
    expected_final_6 = min(150, expected_with_ext_6)  # Plafonné à 150
    
    print(f"Threshold de base: {expected_base_6}")
    print(f"Avec 100% extension: {expected_with_ext_6}")
    print(f"Plafonné à 150: {expected_final_6}")
    print(f"Threshold calculé: {threshold_6}")
    
    print("\n=== Vérifications ===")
    
    # Vérification 1: Moyenne simple
    if abs(threshold_1 - expected_1) < 1:
        print("✅ SUCCÈS: Moyenne simple calculée correctement")
    else:
        print(f"❌ ÉCHEC: Moyenne simple = {threshold_1} au lieu de {expected_1}")
    
    # Vérification 2: Extension 20%
    if abs(threshold_2 - expected_final_2) < 1:
        print("✅ SUCCÈS: Extension 20% calculée correctement")
    else:
        print(f"❌ ÉCHEC: Extension 20% = {threshold_2} au lieu de {expected_final_2}")
    
    # Vérification 3: Contraste élevé
    if abs(threshold_3 - expected_3) < 1:
        print("✅ SUCCÈS: Contraste élevé géré correctement")
    else:
        print(f"❌ ÉCHEC: Contraste élevé = {threshold_3} au lieu de {expected_3}")
    
    # Vérification 4: Pixels identiques
    if threshold_4 == expected_4:
        print("✅ SUCCÈS: Pixels identiques gérés correctement")
    else:
        print(f"❌ ÉCHEC: Pixels identiques = {threshold_4} au lieu de {expected_4}")
    
    # Vérification 5: Plafonnement
    if threshold_6 == expected_final_6:
        print("✅ SUCCÈS: Plafonnement fonctionne correctement")
    else:
        print(f"❌ ÉCHEC: Plafonnement = {threshold_6} au lieu de {expected_final_6}")
    
    # Vérification 6: Plus de pixels capturés avec extension
    if len(unique_captured_ext) >= len(unique_captured):
        print("✅ SUCCÈS: Extension capture plus ou autant de pixels")
    else:
        print("❌ ÉCHEC: Extension capture moins de pixels")
    
    print("\n=== Test terminé ===")

if __name__ == "__main__":
    test_average_threshold()
