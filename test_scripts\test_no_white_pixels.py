#!/usr/bin/env python3
"""
Test pour vérifier que les pixels complètement blancs (255) ne sont jamais capturés
"""

import numpy as np
from utils.helpers import calculate_auto_threshold

def test_no_white_pixels():
    print("=== Test d'exclusion des pixels blancs (255) ===")
    
    # Test 1: Threshold qui pourrait dépasser 254 avec un pourcentage élevé
    print("\n1. Test avec threshold qui pourrait dépasser 254:")
    test_image_1 = np.full((10, 10), 255, dtype=np.uint8)  # Tout blanc
    test_image_1[5, 4] = 200  # Premier pixel
    test_image_1[5, 5] = 220  # Deuxième pixel
    test_image_1[5, 6] = 240  # Troisième pixel (proche de 255)
    
    polygon_points = [(0, 0), (10, 0), (10, 10), (0, 10), (0, 0)]
    
    # Avec un pourcentage élevé qui pourrait faire dépasser 254
    threshold_1 = calculate_auto_threshold(test_image_1, polygon_points, 100)
    print(f"Pixels foncés: [200, 220, 240]")
    print(f"3ème pixel: 240, avec 100%: 240 + 240 = 480")
    print(f"Threshold calculé: {threshold_1} (devrait être plafonné à 254)")
    
    # Vérifier qu'aucun pixel blanc n'est capturé
    binary_mask_1 = (test_image_1 <= threshold_1).astype(np.uint8) * 255
    white_pixels_captured = np.sum((test_image_1 == 255) & (binary_mask_1 == 255))
    total_pixels_captured = np.sum(binary_mask_1 == 255)
    
    print(f"Pixels blancs (255) capturés: {white_pixels_captured}")
    print(f"Total pixels capturés: {total_pixels_captured}")
    
    # Test 2: Cas normal avec des pixels proches de 255
    print("\n2. Test avec pixels proches de 255:")
    test_image_2 = np.full((10, 10), 255, dtype=np.uint8)  # Tout blanc
    test_image_2[5, 4] = 250  # Premier pixel
    test_image_2[5, 5] = 252  # Deuxième pixel
    test_image_2[5, 6] = 254  # Troisième pixel
    
    threshold_2 = calculate_auto_threshold(test_image_2, polygon_points, 20)
    print(f"Pixels foncés: [250, 252, 254]")
    print(f"3ème pixel: 254, avec 20%: 254 + 50.8 = 304.8")
    print(f"Threshold calculé: {threshold_2} (devrait être plafonné à 254)")
    
    binary_mask_2 = (test_image_2 <= threshold_2).astype(np.uint8) * 255
    white_pixels_captured_2 = np.sum((test_image_2 == 255) & (binary_mask_2 == 255))
    captured_values_2 = test_image_2[binary_mask_2 == 255]
    
    print(f"Pixels blancs (255) capturés: {white_pixels_captured_2}")
    print(f"Valeurs capturées: {sorted(np.unique(captured_values_2))}")
    
    # Test 3: Cas extrême avec seulement des pixels très clairs
    print("\n3. Test avec seulement des pixels très clairs:")
    test_image_3 = np.full((10, 10), 255, dtype=np.uint8)  # Tout blanc
    test_image_3[5, 4] = 253  # Premier pixel
    test_image_3[5, 5] = 254  # Deuxième pixel
    # Pas de 3ème pixel foncé, le reste est blanc (255)
    
    threshold_3 = calculate_auto_threshold(test_image_3, polygon_points, 50)
    print(f"Pixels foncés: [253, 254]")
    print(f"Threshold calculé: {threshold_3}")
    
    binary_mask_3 = (test_image_3 <= threshold_3).astype(np.uint8) * 255
    white_pixels_captured_3 = np.sum((test_image_3 == 255) & (binary_mask_3 == 255))
    captured_values_3 = test_image_3[binary_mask_3 == 255]
    
    print(f"Pixels blancs (255) capturés: {white_pixels_captured_3}")
    print(f"Valeurs capturées: {sorted(np.unique(captured_values_3))}")
    
    # Test 4: Vérification que 254 peut être capturé mais pas 255
    print("\n4. Test de la limite exacte (254 vs 255):")
    test_image_4 = np.full((10, 10), 255, dtype=np.uint8)  # Tout blanc
    test_image_4[5, 5] = 254  # Un pixel à 254
    
    threshold_4 = calculate_auto_threshold(test_image_4, polygon_points, 0)
    print(f"Pixel à 254, threshold calculé: {threshold_4}")
    
    binary_mask_4 = (test_image_4 <= threshold_4).astype(np.uint8) * 255
    pixels_254_captured = np.sum((test_image_4 == 254) & (binary_mask_4 == 255))
    pixels_255_captured = np.sum((test_image_4 == 255) & (binary_mask_4 == 255))
    
    print(f"Pixels 254 capturés: {pixels_254_captured}")
    print(f"Pixels 255 capturés: {pixels_255_captured}")
    
    print("\n=== Vérifications ===")
    
    if threshold_1 <= 254:
        print("✅ SUCCÈS: Threshold plafonné à 254 même avec pourcentage élevé")
    else:
        print(f"❌ ÉCHEC: Threshold = {threshold_1} dépasse 254")
    
    if white_pixels_captured == 0:
        print("✅ SUCCÈS: Aucun pixel blanc (255) capturé dans le test 1")
    else:
        print(f"❌ ÉCHEC: {white_pixels_captured} pixels blancs capturés dans le test 1")
        
    if white_pixels_captured_2 == 0:
        print("✅ SUCCÈS: Aucun pixel blanc (255) capturé dans le test 2")
    else:
        print(f"❌ ÉCHEC: {white_pixels_captured_2} pixels blancs capturés dans le test 2")
        
    if white_pixels_captured_3 == 0:
        print("✅ SUCCÈS: Aucun pixel blanc (255) capturé dans le test 3")
    else:
        print(f"❌ ÉCHEC: {white_pixels_captured_3} pixels blancs capturés dans le test 3")
        
    if pixels_254_captured > 0 and pixels_255_captured == 0:
        print("✅ SUCCÈS: Pixels 254 capturés mais pas les pixels 255")
    else:
        print(f"❌ ÉCHEC: Problème avec la limite 254/255")
    
    print("\n=== Test terminé ===")

if __name__ == "__main__":
    test_no_white_pixels()
