#!/usr/bin/env python3
"""
Script de test pour la fonction de calcul automatique du threshold.
"""

import numpy as np
import cv2
from utils.helpers import calculate_auto_threshold

def create_test_image():
    """Crée une image de test avec des zones de différentes intensités."""
    # Créer une image de 200x200 pixels avec 99 pixels blancs et 1 pixel noir
    img = np.ones((200, 200), dtype=np.uint8) * 255  # Fond blanc (255)

    # Ajouter un seul pixel noir au centre pour tester le cas extrême
    img[100, 100] = 1  # Un pixel noir (valeur 1)

    # Ajouter quelques pixels gris pour tester les extensions
    img[99:102, 99:102] = [
        [50, 1, 50],    # Ligne du haut
        [1, 1, 1],      # Ligne du milieu avec le pixel noir central
        [50, 1, 50]     # Ligne du bas
    ]

    return img

def test_auto_threshold():
    """Test la fonction de calcul automatique du threshold."""
    print("=== Test du calcul automatique du threshold ===")
    print("Cas de test: 99 pixels blancs (255) et quelques pixels noirs (1)")

    # C<PERSON>er une image de test
    test_img = create_test_image()

    # Définir un polygone qui englobe la zone avec le pixel noir
    polygon_points = [
        (95, 95),
        (105, 95),
        (105, 105),
        (95, 105),
        (95, 95)  # Fermer le polygone
    ]

    print(f"Image de test créée: {test_img.shape}")
    print(f"Valeur minimale dans la zone: {np.min(test_img[95:105, 95:105])}")
    print(f"Valeur maximale dans la zone: {np.max(test_img[95:105, 95:105])}")
    print(f"Pixels dans la zone de test: {test_img[99:102, 99:102]}")
    print()

    # Tester avec différents pourcentages
    percentages = [0, 10, 20, 30, 50, 100]

    print("Tests avec différents pourcentages:")
    print("Logique: threshold = pixel_le_plus_foncé + (pourcentage% × pixel_le_plus_foncé)")
    print()

    for percentage in percentages:
        threshold = calculate_auto_threshold(test_img, polygon_points, percentage)
        expected = 1 + (percentage / 100.0) * 1  # Calcul attendu
        print(f"Pourcentage: {percentage:3d}% -> Threshold calculé: {threshold:3d} (attendu: {expected:.1f})")

    print()

    # Test avec un polygone vide
    empty_polygon = []
    threshold_empty = calculate_auto_threshold(test_img, empty_polygon, 20)
    print(f"Polygone vide -> Threshold par défaut: {threshold_empty}")

    # Test avec un polygone en dehors de l'image
    outside_polygon = [
        (300, 300),
        (350, 300),
        (350, 350),
        (300, 350),
        (300, 300)
    ]
    threshold_outside = calculate_auto_threshold(test_img, outside_polygon, 20)
    print(f"Polygone en dehors -> Threshold par défaut: {threshold_outside}")

    # Test avec une zone contenant seulement des pixels blancs
    white_polygon = [
        (10, 10),
        (20, 10),
        (20, 20),
        (10, 20),
        (10, 10)
    ]
    threshold_white = calculate_auto_threshold(test_img, white_polygon, 20)
    expected_white = 255 + (20 / 100.0) * 255
    print(f"Zone blanche -> Threshold calculé: {threshold_white} (attendu: {expected_white:.1f})")

    print("=== Test terminé ===")

if __name__ == "__main__":
    test_auto_threshold()
