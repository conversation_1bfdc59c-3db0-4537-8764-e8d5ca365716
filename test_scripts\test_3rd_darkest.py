#!/usr/bin/env python3
"""
Test pour vérifier que le threshold se calcule toujours selon le 3ème pixel le plus foncé
"""

import numpy as np
from utils.helpers import calculate_auto_threshold

def test_3rd_darkest_logic():
    print("=== Test du 3ème pixel le plus foncé ===")
    
    # Test 1: Un seul pixel noir (devrait essayer de prendre le 3ème si possible)
    print("\n1. Test avec un seul pixel noir:")
    test_image_1 = np.full((10, 10), 255, dtype=np.uint8)  # Tout blanc
    test_image_1[5, 5] = 1  # Un pixel noir
    
    polygon_points = [(0, 0), (10, 0), (10, 10), (0, 10), (0, 0)]
    threshold_1 = calculate_auto_threshold(test_image_1, polygon_points, 0)
    print(f"Threshold calculé: {threshold_1}")
    
    # Test 2: Trois pixels de valeurs différentes
    print("\n2. Test avec trois pixels de valeurs différentes:")
    test_image_2 = np.full((10, 10), 255, dtype=np.uint8)  # Tout blanc
    test_image_2[5, 4] = 10  # Premier pixel (le plus foncé)
    test_image_2[5, 5] = 20  # Deuxième pixel
    test_image_2[5, 6] = 30  # Troisième pixel (devrait être le threshold de base)
    
    threshold_2 = calculate_auto_threshold(test_image_2, polygon_points, 0)
    print(f"Threshold calculé: {threshold_2} (devrait être 30 = 3ème plus foncé)")
    
    # Test 3: Cinq pixels de valeurs différentes
    print("\n3. Test avec cinq pixels de valeurs différentes:")
    test_image_3 = np.full((10, 10), 255, dtype=np.uint8)  # Tout blanc
    test_image_3[5, 3] = 5   # 1er plus foncé
    test_image_3[5, 4] = 15  # 2ème plus foncé
    test_image_3[5, 5] = 25  # 3ème plus foncé (devrait être le threshold)
    test_image_3[5, 6] = 35  # 4ème plus foncé
    test_image_3[5, 7] = 45  # 5ème plus foncé
    
    threshold_3 = calculate_auto_threshold(test_image_3, polygon_points, 0)
    print(f"Threshold calculé: {threshold_3} (devrait être 25 = 3ème plus foncé)")
    
    # Test 4: Avec pourcentage sur le 3ème pixel
    print("\n4. Test avec pourcentage 20% sur le 3ème pixel:")
    threshold_4 = calculate_auto_threshold(test_image_3, polygon_points, 20)
    expected_4 = 25 + (20/100.0) * 25  # 25 + 5 = 30
    print(f"Threshold calculé: {threshold_4} (attendu: {expected_4})")
    
    # Test 5: Vérification des pixels capturés
    print("\n5. Vérification des pixels capturés avec le 3ème pixel:")
    binary_mask = (test_image_3 <= threshold_3).astype(np.uint8) * 255
    pixels_captured = np.sum(binary_mask == 255)
    y_coords, x_coords = np.where(binary_mask == 255)
    captured_values = test_image_3[binary_mask == 255]
    print(f"Pixels capturés: {pixels_captured}")
    print(f"Valeurs capturées: {sorted(captured_values)}")
    print(f"Coordonnées: {list(zip(x_coords, y_coords))}")
    
    # Test 6: Cas avec pixels très clairs (> 200)
    print("\n6. Test avec pixels très clairs (> 200):")
    test_image_6 = np.full((10, 10), 255, dtype=np.uint8)  # Tout blanc
    test_image_6[5, 3] = 1    # 1er plus foncé
    test_image_6[5, 4] = 2    # 2ème plus foncé  
    test_image_6[5, 5] = 3    # 3ème plus foncé (devrait être le threshold)
    test_image_6[5, 6] = 250  # Pixel très clair (devrait être ignoré)
    
    threshold_6 = calculate_auto_threshold(test_image_6, polygon_points, 0)
    print(f"Threshold calculé: {threshold_6} (devrait être 3 = 3ème plus foncé, ignorant 250)")
    
    print("\n=== Vérifications ===")
    
    if threshold_2 == 30:
        print("✅ SUCCÈS: Avec 3 pixels [10,20,30], threshold = 30 (3ème plus foncé)")
    else:
        print(f"❌ ÉCHEC: Avec 3 pixels [10,20,30], threshold = {threshold_2} au lieu de 30")
    
    if threshold_3 == 25:
        print("✅ SUCCÈS: Avec 5 pixels [5,15,25,35,45], threshold = 25 (3ème plus foncé)")
    else:
        print(f"❌ ÉCHEC: Avec 5 pixels [5,15,25,35,45], threshold = {threshold_3} au lieu de 25")
        
    if pixels_captured >= 3:
        print("✅ SUCCÈS: Au moins 3 pixels sont capturés")
    else:
        print(f"❌ ÉCHEC: Seulement {pixels_captured} pixels capturés au lieu d'au moins 3")
        
    if threshold_6 == 3:
        print("✅ SUCCÈS: Les pixels très clairs (> 200) sont ignorés")
    else:
        print(f"❌ ÉCHEC: Les pixels très clairs ne sont pas ignorés, threshold = {threshold_6}")
    
    print("\n=== Test terminé ===")

if __name__ == "__main__":
    test_3rd_darkest_logic()
