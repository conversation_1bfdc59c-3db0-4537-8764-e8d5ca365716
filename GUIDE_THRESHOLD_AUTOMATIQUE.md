# Guide d'utilisation : Threshold Automatique

## Vue d'ensemble

La nouvelle fonctionnalité de **Threshold Automatique** permet de calculer automatiquement la valeur du seuil (threshold) basée sur les pixels les plus foncés dans la zone de sélection, avec un pourcentage d'extension configurable.

## Comment ça fonctionne

### Méthodes disponibles

Vous pouvez choisir entre **deux méthodes de calcul** :

#### 🎯 **Méthode "Moyenne min/max"** (recommandée)
- **Principe** : Calcule la moyenne entre le pixel le plus foncé et le pixel le moins foncé
- **Avantage** : S'adapte automatiquement à toute la plage de valeurs dans la zone
- **Idéal pour** : Zones avec dégradés, contrastes variés, analyse générale

#### 🎯 **Méthode "3ème pixel"** (classique)
- **Principe** : Se base sur le 3ème pixel le plus foncé dans la zone
- **Avantage** : Garantit qu'au moins 3 pixels seront capturés, évite les pixels isolés
- **Idéal pour** : Zones avec quelques pixels très foncés, détection précise

### Principe de base

1. **Analyse de la zone de sélection** : L'algorithme analyse tous les pixels dans la zone de sélection (polygone ou rectangle)

2. **Calcul selon la méthode choisie** :
   - **Moyenne** : `(pixel_min + pixel_max) / 2` (excluant le fond blanc 255)
   - **3ème pixel** : Valeur du 3ème pixel le plus foncé (≤ 200)

4. **Application du pourcentage** : Un pourcentage d'extension est appliqué pour inclure des pixels légèrement plus clairs.

5. **Calcul du threshold** :
   ```
   Threshold = min(Max_Threshold, Moyenne_min_max + (Pourcentage/100 × Moyenne_min_max))
   ```

6. **Threshold maximum configurable** : Vous pouvez définir un threshold maximum personnalisé (défaut: 254) pour contrôler précisément quels pixels peuvent être capturés. Cela évite de capturer les pixels non désirés comme le fond blanc.

### Exemples concrets

#### Comparaison des deux méthodes

**Cas typique (pixels variés) : [10, 50, 100, 200]**

🎯 **Méthode "Moyenne min/max"** :
- Pixel le plus foncé = 10, pixel le moins foncé = 200
- Threshold de base = (10 + 200) / 2 = 105
- Avec 20% : 105 + (20/100 × 105) = 126
- **Résultat** : capture les pixels ≤ 126

🎯 **Méthode "3ème pixel"** :
- Pixels triés : [10, 50, 100, 200]
- 3ème pixel le plus foncé = 100
- Avec 20% : 100 + (20/100 × 100) = 120
- **Résultat** : capture les pixels ≤ 120

**Cas avec contraste élevé : [1, 254]**

🎯 **Méthode "Moyenne min/max"** :
- Threshold de base = (1 + 254) / 2 = 127.5 ≈ 128
- **Résultat** : capture environ la moitié de la plage

🎯 **Méthode "3ème pixel"** :
- Un seul pixel foncé (≤ 200) = 1
- Threshold de base = 1
- **Résultat** : capture seulement les pixels très foncés

**Cas avec beaucoup de pixels foncés : [5, 15, 25, 35, 45]**

🎯 **Méthode "Moyenne min/max"** :
- Threshold de base = (5 + 45) / 2 = 25
- **Résultat** : capture la moitié de la plage

🎯 **Méthode "3ème pixel"** :
- 3ème pixel le plus foncé = 25
- **Résultat** : capture au minimum les 3 pixels les plus foncés

### Quelle méthode choisir ?

#### 🎯 **Utilisez "Moyenne min/max" quand :**
- Vous analysez des zones avec **dégradés** ou **contrastes variés**
- Vous voulez un threshold **adaptatif** à toute la plage de valeurs
- Vous travaillez sur des **images naturelles** avec transitions douces
- Vous voulez un comportement **prévisible** et équilibré

#### 🎯 **Utilisez "3ème pixel" quand :**
- Vous cherchez des **objets très foncés** spécifiques
- Vous voulez **garantir** qu'au moins 3 pixels seront capturés
- Vous travaillez sur des **images techniques** avec peu de pixels d'intérêt
- Vous voulez éviter les **pixels isolés** qui ne forment pas de contours

### Avantages généraux
- **Adaptatif** : S'adapte automatiquement au contenu de chaque zone
- **Flexible** : Deux méthodes pour différents cas d'usage
- **Robuste** : Gestion intelligente des pixels de fond
- **Configurable** : Pourcentage et threshold maximum personnalisables

## Interface utilisateur

### Contrôles disponibles

1. **Checkbox "Threshold automatique"** : Cochez cette case pour activer le mode automatique
   - Quand activé : Le slider de threshold manuel est désactivé
   - Quand désactivé : Retour au mode manuel normal

2. **Méthode de calcul** : Choisissez entre les deux méthodes
   - 🔘 **"Moyenne min/max"** : Recommandée pour la plupart des cas
   - 🔘 **"3ème pixel"** : Pour la détection précise d'objets foncés

3. **Contrôle de pourcentage (±)** : Visible uniquement quand le mode automatique est activé
   - Plage : 0% à 500%
   - Valeur par défaut : 20%
   - Contrôle avec flèches pour ajuster de 1 en 1
   - Plus le pourcentage est élevé, plus vous incluez de pixels clairs

4. **Threshold max** : Contrôle configurable pour limiter le threshold calculé
   - Plage : 1 à 255
   - Valeur par défaut : 254
   - Empêche de capturer des pixels au-dessus de cette valeur

### Utilisation

1. **Activez le threshold automatique** en cochant la case
2. **Choisissez la méthode de calcul** :
   - **"Moyenne min/max"** : Pour la plupart des cas (recommandé)
   - **"3ème pixel"** : Pour la détection précise d'objets foncés
3. **Ajustez le pourcentage** selon vos besoins :
   - **0%** : Threshold de base sans extension
   - **10-20%** : Recommandé pour la plupart des cas
   - **30-50%** : Pour inclure une zone plus large
   - **100-200%** : Pour des captures très larges
   - **300-500%** : Pour des captures extrêmement larges (attention au plafonnement)
4. **Configurez le threshold maximum** si nécessaire :
   - **254** : Évite les pixels blancs (recommandé)
   - **200-250** : Pour éviter les pixels très clairs
   - **100-150** : Pour des captures très sélectives
3. **Dessinez votre polygone ou rectangle** comme d'habitude
4. **Le threshold est calculé automatiquement** lors de la fermeture de la forme

## Avantages

- **Précision** : Le threshold s'adapte automatiquement au contenu de chaque zone
- **Consistance** : Même approche pour toutes les sélections similaires
- **Efficacité** : Plus besoin d'ajuster manuellement le threshold pour chaque zone
- **Flexibilité** : Le pourcentage permet d'affiner la sensibilité

## Algorithme de calcul

```
Formule : Threshold = Moyenne_min_max + (Pourcentage/100 × Moyenne_min_max)
Où : Moyenne_min_max = (Pixel_le_plus_foncé + Pixel_le_moins_foncé) / 2

Exemples concrets :

1. Cas avec pixels variés :
   - Pixels dans la zone : [10, 50, 100, 200]
   - Pixel le plus foncé = 10
   - Pixel le moins foncé = 200
   - Moyenne = (10 + 200) / 2 = 105
   - Pourcentage = 20%
   - Threshold calculé = 105 + (20/100 × 105) = 105 + 21 = 126
   - Résultat : Capture tous les pixels ≤ 126

2. Cas avec contraste élevé :
   - Pixels dans la zone : [1, 254]
   - Pixel le plus foncé = 1
   - Pixel le moins foncé = 254
   - Moyenne = (1 + 254) / 2 = 127.5 ≈ 128
   - Pourcentage = 0%
   - Threshold calculé = 128
   - Résultat : Capture environ la moitié de la plage

3. Cas avec pixels identiques :
   - Pixels dans la zone : [100, 100, 100]
   - Pixel le plus foncé = 100
   - Pixel le moins foncé = 100
   - Moyenne = (100 + 100) / 2 = 100
   - Pourcentage = 20%
   - Threshold calculé = 100 + (20/100 × 100) = 120
   - Résultat : Capture tous les pixels ≤ 120

4. Cas avec plafonnement par défaut (max=254) :
   - Pixels dans la zone : [50, 200]
   - Moyenne = (50 + 200) / 2 = 125
   - Pourcentage = 100%
   - Calcul brut = 125 + (100/100 × 125) = 250
   - Threshold final = min(254, 250) = 250
   - Résultat : Capture une large gamme mais évite le fond blanc (255)

5. Cas avec threshold maximum personnalisé (max=150) :
   - Pixels dans la zone : [10, 200]
   - Moyenne = (10 + 200) / 2 = 105
   - Pourcentage = 100%
   - Calcul brut = 105 + (100/100 × 105) = 210
   - Threshold final = min(150, 210) = 150
   - Résultat : Capture contrôlée, plafonnée à 150
```

## Cas d'usage recommandés

### Pourcentage faible (0-10%)
- Détection de défauts très précis
- Zones avec des contrastes très nets
- Quand vous voulez seulement les pixels les plus foncés

### Pourcentage moyen (15-25%)
- Usage général recommandé
- Bon équilibre entre précision et couverture
- Détection de zones d'intérêt avec variations légères

### Pourcentage élevé (30-50%)
- Zones avec des dégradés
- Quand vous voulez capturer une zone plus large
- Détection de zones floues ou avec du bruit

### Pourcentage très élevé (100-200%)
- Zones avec beaucoup de variations
- Capture de zones très larges autour du pixel le plus foncé
- Utile pour des images avec beaucoup de bruit ou de dégradés complexes

### Pourcentage extrême (300-500%)
- Capture quasi-totale de l'image (attention au threshold max)
- Utile pour des analyses exploratoires
- Détection de toutes les zones non-blanches
- **Recommandé** : Utiliser avec un threshold max bas (ex: 150-200) pour éviter de capturer tout

## Notes techniques

- Le threshold calculé est automatiquement affiché dans le champ threshold
- Les paramètres (auto_threshold et threshold_percentage) sont sauvegardés avec chaque polygone
- En cas d'erreur ou de zone vide, une valeur par défaut de 150 est utilisée
- Le calcul se fait en temps réel lors de la fermeture du polygone/rectangle

## Conseils d'utilisation

1. **Testez différents pourcentages** sur une zone représentative avant de traiter toute l'image
2. **Utilisez la prévisualisation** pour vérifier que le masque généré correspond à vos attentes
3. **Combinez avec les autres paramètres** (type de masque, lissage) pour des résultats optimaux
4. **Désactivez le mode automatique** si vous préférez un contrôle manuel précis du threshold
