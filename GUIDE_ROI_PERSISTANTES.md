# 🎯 Guide : ROI Persistantes et Gestion des Zones

## 🚀 **Nouvelles Fonctionnalités Implémentées**

### **1. ROI Persistantes** ✅
- **Conservation automatique** des zones ROI après création
- **Restauration automatique** lors du changement d'image
- **Sélection automatique** des zones conservées

### **2. Bouton de Suppression** ✅
- **Bouton "Supprimer ROI"** pour effacer toutes les zones actives
- **Interface claire** avec information sur les ROI actives

### **3. Affichage Visuel** ✅
- **ROI active** : Ligne verte épaisse pointillée
- **ROI inactives** : Ligne verte claire pointillée
- **Information en temps réel** sur le nombre de ROI

## 🎨 **Interface Utilisateur**

### **Section "Gestion des ROI"**
```
Gestion des ROI:
[Supprimer ROI]  ← Bouton rouge pour effacer toutes les ROI
Aucune ROI active  ← Information sur l'état des ROI
```

### **États d'Affichage**
```
Aucune ROI active                    ← Aucune zone sauvegardée
2 ROI persistante(s) (ROI 1 active) ← 2 zones, la première est active
```

## 🔧 **Fonctionnement Détaillé**

### **1. Création et Sauvegarde Automatique**

#### **Mode Polygone**
1. **Dessiner un polygone** : Clic pour ajouter des points
2. **Fermer le polygone** : Clic près du premier point
3. **Sauvegarde automatique** : La ROI est conservée automatiquement
4. **Traitement automatique** : Détection des défauts dans la zone
5. **Affichage persistant** : Zone visible en vert pointillé

#### **Mode Rectangle**
1. **Premier clic** : Définir le coin de départ
2. **Deuxième clic** : Définir le coin opposé
3. **Sauvegarde automatique** : La ROI rectangle est conservée
4. **Traitement automatique** : Détection des défauts dans la zone
5. **Affichage persistant** : Zone visible en vert pointillé

### **2. Conservation des Paramètres**

**Chaque ROI sauvegarde :**
```python
{
    'points': [(x1, y1), (x2, y2), ...],  # Coordonnées de la zone
    'type': 'polygon' ou 'rectangle',     # Type de ROI
    'parameters': {
        'threshold': 150,                  # Seuil utilisé
        'mask_type': 'polygon',           # Type de masque
        'auto_threshold': True,           # Calcul automatique
        'threshold_method': 'sauvola',    # Méthode de calcul
        'vertical_gap': 5,                # Distance verticale
        # ... tous les autres paramètres
    }
}
```

### **3. Restauration Automatique**

**Lors du changement d'image :**
1. **Chargement de la nouvelle image**
2. **Vérification des ROI** : Coordonnées dans les limites
3. **Application des paramètres** : Utilisation des paramètres sauvegardés
4. **Traitement automatique** : Détection avec les mêmes réglages
5. **Affichage immédiat** : Résultats visibles instantanément

### **4. Gestion Visuelle**

#### **Couleurs des ROI**
- **ROI active** : `#00ff00` (vert vif) - ligne épaisse (3px)
- **ROI inactives** : `#90EE90` (vert clair) - ligne normale (2px)
- **Style** : Ligne pointillée (8, 4) pour visibilité

#### **Superposition des Éléments**
```
Ordre d'affichage (du fond vers le dessus) :
1. Image de base
2. Polygones définitifs (colorés par label)
3. Polygones temporaires (gris pointillé)
4. ROI persistantes (vert pointillé)
5. Polygone en cours de création
```

## 🎯 **Workflow d'Utilisation**

### **Scénario Typique**

#### **1. Première Image**
```
1. Charger une image
2. Dessiner une zone ROI (polygone ou rectangle)
   → Zone automatiquement sauvegardée et traitée
3. Ajuster les paramètres si nécessaire
4. Valider les résultats ("Ajouter au label")
```

#### **2. Images Suivantes**
```
1. Charger la nouvelle image
   → ROI automatiquement restaurée et appliquée
2. Vérifier les résultats
3. Ajuster les paramètres si nécessaire
4. Valider ("Ajouter au label")
```

#### **3. Gestion des ROI**
```
• Ajouter de nouvelles ROI : Dessiner d'autres zones
• Supprimer toutes les ROI : Clic sur "Supprimer ROI"
• Voir l'état : Information en temps réel
```

## 🔧 **Paramètres Techniques**

### **Validation des Coordonnées**
```python
# Vérification automatique lors de la restauration
if 0 <= x < image_width and 0 <= y < image_height:
    # Point valide, ROI restaurée
else:
    # Point hors limites, ROI ignorée
```

### **Gestion des Erreurs**
- **Points hors limites** : ROI ignorée avec avertissement
- **Moins de 3 points** : ROI ignorée
- **Image non chargée** : Restauration différée

### **Performance**
- **Cache des masques** : Évite les recalculs inutiles
- **Traitement par lot** : Toutes les ROI traitées ensemble
- **Mise à jour optimisée** : Affichage groupé

## 🎨 **Avantages Utilisateur**

### **🚀 Gain de Temps**
- **Pas de re-dessin** des zones sur chaque image
- **Paramètres conservés** automatiquement
- **Traitement immédiat** lors du changement d'image

### **🎯 Précision**
- **Zones exactement identiques** sur toutes les images
- **Paramètres cohérents** pour toute la série
- **Résultats reproductibles**

### **🔧 Flexibilité**
- **Ajout de nouvelles ROI** à tout moment
- **Suppression facile** de toutes les zones
- **Paramètres ajustables** par ROI

### **👁️ Visibilité**
- **Affichage clair** des zones actives
- **Information en temps réel** sur l'état
- **Distinction visuelle** entre ROI active/inactives

## 🎯 **Cas d'Usage Optimaux**

### **1. Inspection de Série**
```
Contexte : Analyser la même zone sur 100 images
Avantage : Dessiner une fois, appliquer partout
Gain : 99% de temps économisé sur le dessin
```

### **2. Zones Multiples**
```
Contexte : 3 zones d'intérêt par image
Avantage : Toutes les zones conservées et appliquées
Gain : Workflow cohérent et rapide
```

### **3. Paramètres Complexes**
```
Contexte : Réglages fins de threshold et méthodes
Avantage : Paramètres sauvegardés avec chaque zone
Gain : Pas de re-configuration manuelle
```

## 🚨 **Limitations et Considérations**

### **Taille d'Image**
- **Images de tailles différentes** : ROI peuvent être hors limites
- **Solution** : Vérification automatique et avertissements

### **Nombre de ROI**
- **Pas de limite technique** mais performance peut diminuer
- **Recommandation** : Utiliser "Supprimer ROI" régulièrement

### **Persistance**
- **ROI perdues** à la fermeture de l'application
- **Solution future** : Sauvegarde dans fichier de configuration

## 🎉 **Résumé des Bénéfices**

### **✅ Fonctionnalités Réalisées**
1. **ROI persistantes** entre les images ✅
2. **Sauvegarde automatique** des zones ✅
3. **Restauration automatique** avec paramètres ✅
4. **Bouton de suppression** des zones ✅
5. **Affichage visuel** des ROI actives ✅
6. **Information en temps réel** ✅

### **🚀 Impact sur le Workflow**
- **Gain de temps** : 90%+ sur le re-dessin des zones
- **Précision** : Zones exactement identiques
- **Cohérence** : Paramètres conservés automatiquement
- **Facilité** : Interface intuitive et informative

**Les ROI persistantes transforment l'application en un outil professionnel d'inspection de série !** 🎯
