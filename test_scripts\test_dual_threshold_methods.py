#!/usr/bin/env python3
"""
Test pour vérifier que les deux méthodes de calcul du threshold automatique fonctionnent :
- Méthode "average" : moyenne entre min et max
- Méthode "third_darkest" : basée sur le 3ème pixel le plus foncé
"""

import numpy as np
from utils.helpers import calculate_auto_threshold

def test_dual_threshold_methods():
    print("=== Test des Deux Méthodes de Threshold Automatique ===")
    
    # Test 1: Cas avec pixels variés
    print("\n1. Test avec pixels variés [10, 50, 100, 200]:")
    test_image_1 = np.full((10, 10), 255, dtype=np.uint8)  # Fond blanc
    test_image_1[5, 4] = 10   # Pixel le plus foncé
    test_image_1[5, 5] = 50   # Pixel intermédiaire
    test_image_1[5, 6] = 100  # Pixel intermédiaire
    test_image_1[5, 7] = 200  # Pixel le moins foncé (dans la zone)
    
    polygon_points = [(0, 0), (10, 0), (10, 10), (0, 10), (0, 0)]
    
    # Méthode moyenne
    threshold_avg = calculate_auto_threshold(test_image_1, polygon_points, 0, 254, "average")
    expected_avg = (10 + 200) / 2  # 105
    
    # Méthode 3ème pixel
    threshold_3rd = calculate_auto_threshold(test_image_1, polygon_points, 0, 254, "third_darkest")
    expected_3rd = 100  # 3ème pixel le plus foncé
    
    print(f"Méthode moyenne: {threshold_avg} (attendu: {expected_avg})")
    print(f"Méthode 3ème pixel: {threshold_3rd} (attendu: {expected_3rd})")
    
    # Test 2: Avec extension 20%
    print("\n2. Test avec extension 20%:")
    
    threshold_avg_ext = calculate_auto_threshold(test_image_1, polygon_points, 20, 254, "average")
    expected_avg_ext = 105 + (105 * 0.20)  # 126
    
    threshold_3rd_ext = calculate_auto_threshold(test_image_1, polygon_points, 20, 254, "third_darkest")
    expected_3rd_ext = 100 + (100 * 0.20)  # 120
    
    print(f"Méthode moyenne + 20%: {threshold_avg_ext} (attendu: {expected_avg_ext})")
    print(f"Méthode 3ème pixel + 20%: {threshold_3rd_ext} (attendu: {expected_3rd_ext})")
    
    # Test 3: Cas avec contraste élevé
    print("\n3. Test avec contraste élevé [1, 254]:")
    test_image_3 = np.full((10, 10), 255, dtype=np.uint8)  # Fond blanc
    test_image_3[5, 4] = 1    # Pixel très foncé
    test_image_3[5, 5] = 254  # Pixel très clair (mais pas blanc)
    
    threshold_avg_3 = calculate_auto_threshold(test_image_3, polygon_points, 0, 254, "average")
    expected_avg_3 = (1 + 254) / 2  # 127.5
    
    threshold_3rd_3 = calculate_auto_threshold(test_image_3, polygon_points, 0, 254, "third_darkest")
    expected_3rd_3 = 1  # Seul pixel vraiment foncé
    
    print(f"Méthode moyenne: {threshold_avg_3} (attendu: {expected_avg_3})")
    print(f"Méthode 3ème pixel: {threshold_3rd_3} (attendu: {expected_3rd_3})")
    
    # Test 4: Cas avec beaucoup de pixels foncés
    print("\n4. Test avec beaucoup de pixels foncés [5, 15, 25, 35, 45]:")
    test_image_4 = np.full((10, 10), 255, dtype=np.uint8)  # Fond blanc
    test_image_4[5, 3] = 5    # 1er pixel le plus foncé
    test_image_4[5, 4] = 15   # 2ème pixel le plus foncé
    test_image_4[5, 5] = 25   # 3ème pixel le plus foncé
    test_image_4[5, 6] = 35   # 4ème pixel
    test_image_4[5, 7] = 45   # 5ème pixel
    
    threshold_avg_4 = calculate_auto_threshold(test_image_4, polygon_points, 0, 254, "average")
    expected_avg_4 = (5 + 45) / 2  # 25
    
    threshold_3rd_4 = calculate_auto_threshold(test_image_4, polygon_points, 0, 254, "third_darkest")
    expected_3rd_4 = 25  # 3ème pixel le plus foncé
    
    print(f"Méthode moyenne: {threshold_avg_4} (attendu: {expected_avg_4})")
    print(f"Méthode 3ème pixel: {threshold_3rd_4} (attendu: {expected_3rd_4})")
    
    # Test 5: Cas avec pixels identiques
    print("\n5. Test avec pixels identiques [100, 100, 100]:")
    test_image_5 = np.full((10, 10), 255, dtype=np.uint8)  # Fond blanc
    test_image_5[5, 4] = 100  # Tous les pixels de la zone ont la même valeur
    test_image_5[5, 5] = 100
    test_image_5[5, 6] = 100
    
    threshold_avg_5 = calculate_auto_threshold(test_image_5, polygon_points, 0, 254, "average")
    expected_avg_5 = (100 + 100) / 2  # 100
    
    threshold_3rd_5 = calculate_auto_threshold(test_image_5, polygon_points, 0, 254, "third_darkest")
    expected_3rd_5 = 100  # 3ème pixel (même valeur)
    
    print(f"Méthode moyenne: {threshold_avg_5} (attendu: {expected_avg_5})")
    print(f"Méthode 3ème pixel: {threshold_3rd_5} (attendu: {expected_3rd_5})")
    
    # Test 6: Plafonnement avec les deux méthodes
    print("\n6. Test plafonnement (max_threshold=150):")
    
    threshold_avg_cap = calculate_auto_threshold(test_image_1, polygon_points, 100, 150, "average")
    # Moyenne = 105, avec 100% = 210, plafonné à 150
    
    threshold_3rd_cap = calculate_auto_threshold(test_image_1, polygon_points, 100, 150, "third_darkest")
    # 3ème pixel = 100, avec 100% = 200, plafonné à 150
    
    print(f"Méthode moyenne plafonnée: {threshold_avg_cap} (attendu: 150)")
    print(f"Méthode 3ème pixel plafonnée: {threshold_3rd_cap} (attendu: 150)")
    
    print("\n=== Vérifications ===")
    
    # Vérifications
    tests_passed = 0
    total_tests = 12
    
    # Test 1
    if abs(threshold_avg - expected_avg) < 1:
        print("✅ SUCCÈS: Méthode moyenne - cas variés")
        tests_passed += 1
    else:
        print(f"❌ ÉCHEC: Méthode moyenne - cas variés = {threshold_avg} au lieu de {expected_avg}")
    
    if threshold_3rd == expected_3rd:
        print("✅ SUCCÈS: Méthode 3ème pixel - cas variés")
        tests_passed += 1
    else:
        print(f"❌ ÉCHEC: Méthode 3ème pixel - cas variés = {threshold_3rd} au lieu de {expected_3rd}")
    
    # Test 2
    if abs(threshold_avg_ext - expected_avg_ext) < 1:
        print("✅ SUCCÈS: Méthode moyenne avec extension")
        tests_passed += 1
    else:
        print(f"❌ ÉCHEC: Méthode moyenne avec extension = {threshold_avg_ext} au lieu de {expected_avg_ext}")
    
    if abs(threshold_3rd_ext - expected_3rd_ext) < 1:
        print("✅ SUCCÈS: Méthode 3ème pixel avec extension")
        tests_passed += 1
    else:
        print(f"❌ ÉCHEC: Méthode 3ème pixel avec extension = {threshold_3rd_ext} au lieu de {expected_3rd_ext}")
    
    # Test 3
    if abs(threshold_avg_3 - expected_avg_3) < 1:
        print("✅ SUCCÈS: Méthode moyenne - contraste élevé")
        tests_passed += 1
    else:
        print(f"❌ ÉCHEC: Méthode moyenne - contraste élevé = {threshold_avg_3} au lieu de {expected_avg_3}")
    
    if threshold_3rd_3 == expected_3rd_3:
        print("✅ SUCCÈS: Méthode 3ème pixel - contraste élevé")
        tests_passed += 1
    else:
        print(f"❌ ÉCHEC: Méthode 3ème pixel - contraste élevé = {threshold_3rd_3} au lieu de {expected_3rd_3}")
    
    # Test 4
    if abs(threshold_avg_4 - expected_avg_4) < 1:
        print("✅ SUCCÈS: Méthode moyenne - beaucoup de pixels")
        tests_passed += 1
    else:
        print(f"❌ ÉCHEC: Méthode moyenne - beaucoup de pixels = {threshold_avg_4} au lieu de {expected_avg_4}")
    
    if threshold_3rd_4 == expected_3rd_4:
        print("✅ SUCCÈS: Méthode 3ème pixel - beaucoup de pixels")
        tests_passed += 1
    else:
        print(f"❌ ÉCHEC: Méthode 3ème pixel - beaucoup de pixels = {threshold_3rd_4} au lieu de {expected_3rd_4}")
    
    # Test 5
    if threshold_avg_5 == expected_avg_5:
        print("✅ SUCCÈS: Méthode moyenne - pixels identiques")
        tests_passed += 1
    else:
        print(f"❌ ÉCHEC: Méthode moyenne - pixels identiques = {threshold_avg_5} au lieu de {expected_avg_5}")
    
    if threshold_3rd_5 == expected_3rd_5:
        print("✅ SUCCÈS: Méthode 3ème pixel - pixels identiques")
        tests_passed += 1
    else:
        print(f"❌ ÉCHEC: Méthode 3ème pixel - pixels identiques = {threshold_3rd_5} au lieu de {expected_3rd_5}")
    
    # Test 6
    if threshold_avg_cap == 150:
        print("✅ SUCCÈS: Méthode moyenne - plafonnement")
        tests_passed += 1
    else:
        print(f"❌ ÉCHEC: Méthode moyenne - plafonnement = {threshold_avg_cap} au lieu de 150")
    
    if threshold_3rd_cap == 150:
        print("✅ SUCCÈS: Méthode 3ème pixel - plafonnement")
        tests_passed += 1
    else:
        print(f"❌ ÉCHEC: Méthode 3ème pixel - plafonnement = {threshold_3rd_cap} au lieu de 150")
    
    print(f"\n=== Résultat Final: {tests_passed}/{total_tests} tests réussis ===")
    
    if tests_passed == total_tests:
        print("🎉 TOUS LES TESTS SONT RÉUSSIS ! Les deux méthodes fonctionnent parfaitement.")
    else:
        print(f"⚠️  {total_tests - tests_passed} test(s) ont échoué. Vérification nécessaire.")

if __name__ == "__main__":
    test_dual_threshold_methods()
