#!/usr/bin/env python3
"""
Test pour vérifier que le threshold maximum est configurable
"""

import numpy as np
from utils.helpers import calculate_auto_threshold

def test_configurable_max_threshold():
    print("=== Test du threshold maximum configurable ===")
    
    # Créer une image de test
    test_image = np.full((10, 10), 255, dtype=np.uint8)  # Tout blanc
    test_image[5, 4] = 100  # Premier pixel
    test_image[5, 5] = 150  # Deuxième pixel
    test_image[5, 6] = 200  # Troisième pixel
    
    polygon_points = [(0, 0), (10, 0), (10, 10), (0, 10), (0, 0)]
    
    # Test 1: Threshold maximum par défaut (254)
    print("\n1. Test avec threshold maximum par défaut (254):")
    threshold_1 = calculate_auto_threshold(test_image, polygon_points, 100)  # 100% d'extension
    print(f"3ème pixel: 200, avec 100%: 200 + 200 = 400")
    print(f"Threshold calculé avec max=254: {threshold_1}")
    
    # Test 2: Threshold maximum personnalisé (220)
    print("\n2. Test avec threshold maximum personnalisé (220):")
    threshold_2 = calculate_auto_threshold(test_image, polygon_points, 100, max_threshold=220)
    print(f"3ème pixel: 200, avec 100%: 200 + 200 = 400")
    print(f"Threshold calculé avec max=220: {threshold_2}")
    
    # Test 3: Threshold maximum très bas (150)
    print("\n3. Test avec threshold maximum très bas (150):")
    threshold_3 = calculate_auto_threshold(test_image, polygon_points, 50, max_threshold=150)
    print(f"3ème pixel: 200, avec 50%: 200 + 100 = 300")
    print(f"Threshold calculé avec max=150: {threshold_3}")
    
    # Test 4: Threshold maximum qui n'affecte pas le résultat
    print("\n4. Test avec threshold maximum élevé (pas d'effet):")
    threshold_4 = calculate_auto_threshold(test_image, polygon_points, 10, max_threshold=250)
    print(f"3ème pixel: 200, avec 10%: 200 + 20 = 220")
    print(f"Threshold calculé avec max=250: {threshold_4}")
    
    # Test 5: Vérification des pixels capturés avec différents max
    print("\n5. Vérification des pixels capturés:")
    
    # Avec max=220
    binary_mask_220 = (test_image <= threshold_2).astype(np.uint8) * 255
    captured_values_220 = test_image[binary_mask_220 == 255]
    print(f"Avec max=220 (threshold={threshold_2}): {sorted(np.unique(captured_values_220))}")
    
    # Avec max=150
    binary_mask_150 = (test_image <= threshold_3).astype(np.uint8) * 255
    captured_values_150 = test_image[binary_mask_150 == 255]
    print(f"Avec max=150 (threshold={threshold_3}): {sorted(np.unique(captured_values_150))}")
    
    # Test 6: Cas extrême avec max_threshold = 1
    print("\n6. Test avec threshold maximum extrême (1):")
    test_image_extreme = np.full((10, 10), 255, dtype=np.uint8)
    test_image_extreme[5, 4] = 1   # Premier pixel
    test_image_extreme[5, 5] = 2   # Deuxième pixel
    test_image_extreme[5, 6] = 3   # Troisième pixel
    
    threshold_extreme = calculate_auto_threshold(test_image_extreme, polygon_points, 0, max_threshold=1)
    print(f"3ème pixel: 3, avec 0%: 3 + 0 = 3")
    print(f"Threshold calculé avec max=1: {threshold_extreme}")
    
    binary_mask_extreme = (test_image_extreme <= threshold_extreme).astype(np.uint8) * 255
    captured_values_extreme = test_image_extreme[binary_mask_extreme == 255]
    print(f"Pixels capturés: {sorted(np.unique(captured_values_extreme))}")
    
    print("\n=== Vérifications ===")
    
    if threshold_1 == 254:
        print("✅ SUCCÈS: Threshold par défaut plafonné à 254")
    else:
        print(f"❌ ÉCHEC: Threshold par défaut = {threshold_1} au lieu de 254")
    
    if threshold_2 == 220:
        print("✅ SUCCÈS: Threshold personnalisé plafonné à 220")
    else:
        print(f"❌ ÉCHEC: Threshold personnalisé = {threshold_2} au lieu de 220")
        
    if threshold_3 == 150:
        print("✅ SUCCÈS: Threshold très bas plafonné à 150")
    else:
        print(f"❌ ÉCHEC: Threshold très bas = {threshold_3} au lieu de 150")
        
    if threshold_4 == 220:  # 200 + 20 = 220, pas de plafonnement
        print("✅ SUCCÈS: Threshold élevé non affecté par le plafond")
    else:
        print(f"❌ ÉCHEC: Threshold élevé = {threshold_4} au lieu de 220")
        
    if threshold_extreme == 1:
        print("✅ SUCCÈS: Threshold extrême plafonné à 1")
    else:
        print(f"❌ ÉCHEC: Threshold extrême = {threshold_extreme} au lieu de 1")
        
    # Vérifier que seul le pixel à 1 est capturé dans le cas extrême
    if len(np.unique(captured_values_extreme)) == 1 and captured_values_extreme[0] == 1:
        print("✅ SUCCÈS: Seul le pixel à 1 est capturé avec max=1")
    else:
        print(f"❌ ÉCHEC: Pixels capturés incorrects avec max=1: {np.unique(captured_values_extreme)}")
    
    print("\n=== Test terminé ===")

if __name__ == "__main__":
    test_configurable_max_threshold()
