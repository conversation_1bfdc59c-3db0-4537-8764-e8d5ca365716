#!/usr/bin/env python3
"""
Script pour convertir les volumes NPZ avec clé 'volume' vers le format Sentinel (clé 'arr_0').
Peut traiter un fichier unique ou un dossier entier.
"""
import numpy as np
import argparse
import sys
from pathlib import Path

def convert_volume_file(file_path):
    """Convertit un fichier NPZ unique"""
    try:
        z = np.load(file_path)
        if hasattr(z, "files") and "volume" in z.files:
            arr = z["volume"]
            # écrase l'existant avec 'arr_0'
            np.savez_compressed(file_path, arr)
            print(f"[OK] Converti -> {file_path}")
            return True
        else:
            print(f"[WARN] Pas de cle 'volume' dans {file_path}")
            return False
    except Exception as e:
        print(f"[ERROR] Erreur lors de la conversion de {file_path}: {e}")
        return False

def convert_volume_directory(directory_path):
    """Convertit tous les fichiers NPZ d'un dossier"""
    directory = Path(directory_path)
    if not directory.exists():
        print(f"[ERROR] Le dossier {directory_path} n'existe pas")
        return False

    npz_files = list(directory.glob("*.npz"))
    if not npz_files:
        print(f"[WARN] Aucun fichier NPZ trouve dans {directory_path}")
        return False

    print(f"[INFO] Traitement de {len(npz_files)} fichiers NPZ dans {directory_path}")

    success_count = 0
    for file_path in npz_files:
        if convert_volume_file(file_path):
            success_count += 1

    print(f"[OK] {success_count}/{len(npz_files)} fichiers convertis avec succes")
    return success_count > 0

def main():
    """Fonction principale avec arguments en ligne de commande"""
    parser = argparse.ArgumentParser(description="Convertit les volumes NPZ avec clé 'volume' vers le format Sentinel (clé 'arr_0')")
    parser.add_argument("path", help="Fichier NPZ ou dossier contenant des fichiers NPZ")

    args = parser.parse_args()

    path = Path(args.path)

    if not path.exists():
        print(f"[ERROR] Le chemin {args.path} n'existe pas")
        sys.exit(1)

    if path.is_file():
        if path.suffix.lower() == '.npz':
            success = convert_volume_file(path)
        else:
            print(f"[ERROR] Le fichier {args.path} n'est pas un fichier NPZ")
            sys.exit(1)
    elif path.is_dir():
        success = convert_volume_directory(path)
    else:
        print(f"[ERROR] Le chemin {args.path} n'est ni un fichier ni un dossier")
        sys.exit(1)

    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
