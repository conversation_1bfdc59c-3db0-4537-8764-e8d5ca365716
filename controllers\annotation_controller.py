# === controller/annotation_controller.py ===
from typing import List, Dict, Tuple, Optional
import logging
import os
import cv2
import numpy as np
from tkinter import Tk
from views.annotation_view import AnnotationView
from models.mask_model import MaskModel
from services.image_loader_service import load_images_from_folder
from services.export_service import export_masks, export_individual_masks, export_all_annotations_to_json
from services.overlay_service import OverlayManager
from services.json_service import JsonExporter, generate_json_filename

from config.constants import CLASS_MAP, LABEL_SETTINGS
from services.export_service import load_annotations_from_json


class AnnotationController:
    """
    Contrôleur principal de l'application d'annotation.
    Coordonne les interactions entre le modèle et la vue.
    """
    
    def __init__(self, image_paths=None):
        """
        Initialise le contrôleur avec le modèle et la vue.
        
        Args:
            image_paths: Liste des chemins d'images (optionnel)
        """
        # 1) Créer la fenêtre principale Tk
        self._root = Tk()
        self._root.title("Polygon Mask Editor")
        self._root.minsize(800, 600)
        
        # 2) Initialiser le modèle
        self._model = MaskModel(
            image_paths=image_paths,
            class_map=CLASS_MAP,
            label_settings=LABEL_SETTINGS
        )
        
        # 3) Initialiser la vue en lui passant le root et le controller
        self._view = AnnotationView(root=self._root, controller=self)
        self._overlay_manager = OverlayManager(class_map=CLASS_MAP)

        # Initialiser le logger (la configuration est faite dans __main__.py)
        self._logger = logging.getLogger(__name__)

        # lier le slider de threshold
        self._view.threshold_slider.configure(
            command=self._on_threshold_change
        )

    def run(self):
        """Démarre la boucle Tkinter."""
        self._root.mainloop()

    def open_folder(self, folder: str) -> None:
        """
        Ouvre un dossier d'images et charge la première image.
        
        Args:
            folder: Chemin du dossier contenant les images
        """
        try:
            # Charger la liste des images
            image_list = load_images_from_folder(folder)
            if not image_list:
                self._logger.warning(f"Aucune image trouvée dans le dossier: {folder}")
                return
                
            # Mettre à jour le modèle
            self._model.image_list = image_list
            self._model.current_index = 0
            
            # Charger la première image
            self.load_image()
            self._logger.info(f"Dossier ouvert: {folder}")
            
        except Exception as e:
            self._logger.error(f"Erreur lors de l'ouverture du dossier: {str(e)}")
            # ne pas remonter : on reste dans l'UI

    def load_image(self) -> None:
        """Charge l'image courante et met à jour l'affichage."""
        try:
            # Charger l'image dans le modèle
            self._model.load_current_image()

            # Mettre à jour l'affichage
            image_with_margin = self._model.image_with_margin()
            if image_with_margin is None:
                self._logger.error("Impossible d'obtenir l'image avec marge")
                return

            self._view.update_filename(self._model.current_filename())
            self._view.update_image_display(image_with_margin)

            # Mettre à jour l'affichage du label actuel (revient à frontwall)
            self._view.update_current_label_display()

            self._logger.info(f"Image chargée: {self._model.current_filename()}")

        except Exception as e:
            self._logger.error(f"Erreur lors du chargement de l'image: {str(e)}")
            # on ne lève pas, pour que l'interface reste opérationnelle

    def next_image(self) -> None:
        """Passe à l'image suivante si disponible."""
        if self._model.current_index < len(self._model.image_list) - 1:
            self._model.current_index += 1
            self.load_image()
            self._logger.info("Passage à l'image suivante")

    def previous_image(self) -> None:
        """Retourne à l'image précédente si disponible."""
        if self._model.current_index > 0:
            self._model.current_index -= 1
            self.load_image()
            self._logger.info("Retour à l'image précédente")

    def export_masks(self, threshold: int, mask_type: str = "standard", smooth_enabled: bool = False, smooth_radius: int = 0) -> None:
        """
        Exporte les masques avec le seuil, le type et l'arrondissement spécifiés.

        Args:
            threshold: Seuil de binarisation pour l'export
            mask_type: Type de masque ("standard" ou "polygon")
            smooth_enabled: Activer l'arrondissement des contours
            smooth_radius: Rayon d'arrondissement (si activé)
        """
        try:
            export_masks(self._model, threshold, mask_type, smooth_enabled, smooth_radius)
            smooth_info = f", lissage: {smooth_radius}" if smooth_enabled else ", pas de lissage"
            self._logger.info(f"Masques exportés avec le seuil: {threshold}, type: {mask_type}{smooth_info}")
        except Exception as e:
            self._logger.error(f"Erreur lors de l'export des masques: {str(e)}")
            # ne pas remonter : on reste dans l'UI

    def export_annotations_to_json(self, label_settings: dict) -> str:
        """
        Exporte les annotations courantes au format JSON.
        
        Args:
            label_settings: Dictionnaire des paramètres par label
            
        Returns:
            str: Chemin du fichier JSON créé
        """
        try:
            # 1) Calcul du dossier de sortie JSON
            base_folder = os.path.dirname(self._model.image_paths[0])
            json_folder = os.path.join(base_folder, "json")
            os.makedirs(json_folder, exist_ok=True)

            # 2) Instanciation avec le dossier
            exporter = JsonExporter(output_folder=json_folder)

            # 3) Génération d'un nom de fichier JSON cohérent
            image_name = os.path.basename(self._model.image_paths[self._model.current_index])
            filename = generate_json_filename(image_name)
            data_dict = {
                "image": self._model.current_filename(),
                "polygons": self.get_all_polygons(),
                "settings": label_settings,
                "filename": filename
            }

            # 4) Export
            exporter.export(data_dict)
            self._logger.info(f"[OK] JSON sauvé dans : {json_folder}/{filename}")
            return os.path.join(json_folder, filename)

        except Exception as e:
            self._logger.error(f"[ERREUR] Erreur export JSON : {e}")
            # ne pas remonter : on reste dans l'UI

    def export_individual_masks(self, label_settings: dict = None) -> None:
        """
        Exporte les masques avec des paramètres individuels pour chaque polygone.

        Args:
            label_settings: Paramètres par label (obsolète, maintenant les paramètres sont dans les polygones)
        """
        try:
            # Les paramètres sont maintenant stockés avec chaque polygone
            # Passer le modèle directement au service d'export
            export_individual_masks(self._model, {})

            # Log des polygones exportés
            polygons_with_params = self._model.get_all_polygons()
            for label, polygons in polygons_with_params.items():
                if polygons:
                    self._logger.info(f"Export {label}: {len(polygons)} polygones avec paramètres individuels")

        except Exception as e:
            self._logger.error(f"Erreur lors de l'export des masques individuels: {str(e)}")
            # ne pas remonter : on reste dans l'UI

    def create_overlay(self, image_folder: str, mask_folder: str, output_folder: str, alpha: float = 0.4) -> None:
        """
        Crée des overlays pour toutes les images avec leurs masques correspondants.
        
        Args:
            image_folder: Dossier contenant les images
            mask_folder: Dossier contenant les masques
            output_folder: Dossier de sortie pour les overlays
            alpha: Transparence du masque (0-1)
        """
        try:
            # Vérifier que les dossiers existent
            if not os.path.exists(image_folder):
                raise ValueError(f"Dossier des images introuvable: {image_folder}")
            if not os.path.exists(mask_folder):
                raise ValueError(f"Dossier des masques introuvable: {mask_folder}")

            # Charger la liste des images
            image_list = load_images_from_folder(image_folder)
            if not image_list:
                self._logger.warning(f"Aucune image trouvée dans le dossier: {image_folder}")
                return

            # Créer le dossier de sortie avec sous-dossier overlay
            overlay_folder = os.path.join(output_folder, "overlay")
            os.makedirs(overlay_folder, exist_ok=True)
            self._logger.info(f"Dossier de sortie créé: {overlay_folder}")

            # Traiter chaque image
            for image_path in image_list:
                try:
                    # Obtenir le nom du fichier sans extension
                    base_name = os.path.splitext(os.path.basename(image_path))[0]
                    mask_path = os.path.join(mask_folder, f"{base_name}.png")
                    
                    # Vérifier si le masque existe
                    if not os.path.exists(mask_path):
                        self._logger.warning(f"Masque introuvable pour {base_name}")
                        continue

                    # Charger l'image originale
                    original = cv2.imread(image_path)
                    if original is None:
                        self._logger.error(f"Impossible de charger l'image: {image_path}")
                        continue

                    # Charger le masque en niveaux de gris
                    mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
                    if mask is None:
                        self._logger.error(f"Impossible de charger le masque: {mask_path}")
                        continue

                    # Vérifier les valeurs uniques dans le masque
                    unique_vals = np.unique(mask)
                    self._logger.info(f"Valeurs uniques dans le masque {base_name}: {unique_vals}")

                    # Créer l'overlay
                    overlay = self._overlay_manager.create_overlay(original, mask, alpha)
                    
                    # Sauvegarder l'overlay
                    output_path = os.path.join(overlay_folder, f"{base_name}_overlay.png")
                    self._overlay_manager.save_overlay(overlay, output_path)
                    
                    self._logger.info(f"Overlay créé pour {base_name}")

                except Exception as e:
                    self._logger.error(f"Erreur lors du traitement de {image_path}: {str(e)}")
                    continue

            self._logger.info("Création des overlays terminée")

        except Exception as e:
            self._logger.error(f"Erreur lors de la création des overlays: {str(e)}")
            # ne pas remonter : on reste dans l'UI

    def add_temporary_polygon(self, pts: List[Tuple[int, int]], parameters: Dict) -> None:
        """
        Ajoute un polygone temporaire avec ses paramètres.

        Args:
            pts: Liste des points du polygone
            parameters: Paramètres utilisés pour créer ce polygone
        """
        if not pts:
            return
        self._model.add_temporary_polygon(pts, parameters)

    def add_polygon(self, pts: List[Tuple[int, int]]) -> None:
        """
        Ajoute un polygone définitif à l'annotation courante.
        DEPRECATED: Utilisez add_temporary_polygon puis commit_temporary_polygons.

        Args:
            pts: Liste des points du polygone
        """
        if not pts:
            return
        label = self._model.current_label
        self._model.add_polygon(label, pts)

    def switch_label(self) -> None:
        """Change le label actif."""
        # on boucle l'index dans [0, n_labels-1]
        n = len(self._model.VALID_LABELS)
        new_idx = (self._model.current_label_index + 1) % n
        self._model.current_label_index = new_idx
        # mise à jour de l'affichage sans reboucler
        self._view.update_current_label_display()

    def _on_threshold_change(self, raw_value):
        # raw_value vient en chaîne, on convertit et on met à jour le modèle
        value = int(float(raw_value))
        self._model.threshold = value

    def undo_polygon(self) -> None:
        """Supprime le dernier polygone ajouté pour le label courant."""
        label = self._model.current_label
        self._model.remove_last_polygon(label)

    def get_all_polygons(self) -> Dict[str, List[List[Tuple[int, int]]]]:
        """
        Retourne tous les polygones annotés définitifs (points seulement pour compatibilité).

        Returns:
            Dict[str, List[List[Tuple[int, int]]]]: Dictionnaire des polygones par label
        """
        return self._model.get_all_polygons_points_only()

    def get_all_polygons_with_parameters(self) -> Dict[str, List[Dict]]:
        """
        Retourne tous les polygones avec leurs paramètres.

        Returns:
            Dict[str, List[Dict]]: Dictionnaire des polygones avec paramètres par label
        """
        return self._model.get_all_polygons()

    def get_temporary_polygons(self) -> List[Dict]:
        """Retourne la liste des polygones temporaires."""
        return self._model.get_temporary_polygons()

    def clear_temporary_polygons(self) -> None:
        """Supprime tous les polygones temporaires."""
        self._model.clear_temporary_polygons()

    def commit_temporary_polygons(self) -> None:
        """Valide les polygones temporaires et les ajoute aux polygones définitifs."""
        self._model.commit_temporary_polygons()

    def get_current_label(self) -> str:
        """Retourne le label d'annotation courant."""
        return self._model.current_label

    def export_all_annotations_to_json(self, label_settings: dict) -> str:
        """
        Exporte toutes les annotations vers un fichier JSON.

        Args:
            label_settings: Dictionnaire des paramètres par label

        Returns:
            Chemin du fichier JSON généré
        """
        try:
            json_path = export_all_annotations_to_json(self._model, label_settings)
            self._logger.info(f"Export JSON complet terminé: {json_path}")
            return json_path
        except Exception as e:
            self._logger.error(f"Erreur lors de l'export JSON complet: {str(e)}")
            # ne pas remonter : on reste dans l'UI

    def load_annotations_from_json(self, json_path: str) -> bool:
        """
        Charge les annotations depuis un fichier JSON.

        Args:
            json_path: Chemin vers le fichier JSON

        Returns:
            True si le chargement a réussi
        """
        try:
            polygons = load_annotations_from_json(json_path, self._model)

            # Remplacer les polygones actuels
            self._model.set_polygons(polygons)

            self._logger.info(f"Annotations chargées depuis JSON: {json_path}")
            return True
        except Exception as e:
            self._logger.error(f"Erreur lors du chargement JSON: {str(e)}")
            # ne pas remonter : on reste dans l'UI


